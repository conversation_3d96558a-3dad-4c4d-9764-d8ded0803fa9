/*
 Navicat Premium Dump SQL

 Source Server         : ************_v8.0
 Source Server Type    : MySQL
 Source Server Version : 80040 (8.0.40)
 Source Host           : ************:3307
 Source Schema         : cm2

 Target Server Type    : MySQL
 Target Server Version : 80040 (8.0.40)
 File Encoding         : 65001

 Date: 14/07/2025 11:06:59
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for cm_application_manage
-- ----------------------------
DROP TABLE IF EXISTS `cm_application_manage`;
CREATE TABLE `cm_application_manage`  (
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `application_name` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '应用名称',
  `business_owner` json NULL COMMENT 'BusinessOwner',
  `team_leader` json NULL COMMENT 'TeamLeader',
  `team_id` bigint NULL DEFAULT NULL COMMENT 'Team_ID',
  `team_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Team_Name',
  `category_id` bigint NULL DEFAULT NULL COMMENT '分类id',
  `category_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分类',
  `is_key_project` tinyint NULL DEFAULT NULL COMMENT '核心项目（1-是 2-否）',
  `is_external_system` tinyint NULL DEFAULT NULL COMMENT '外部项目（1-是 2-否）',
  `maintenance_level` tinyint NULL DEFAULT NULL COMMENT '维护影响等级（1-高 2-中 3-低)）',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态（1-可用 2-非可用）',
  `simple_check_list_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '简单检查表ids',
  `full_check_list_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '全量检查表ids',
  `freeze_area_list` json NULL COMMENT '封网地区',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1939514151239962627 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '应用管理表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_change_delay
-- ----------------------------
DROP TABLE IF EXISTS `cm_change_delay`;
CREATE TABLE `cm_change_delay`  (
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `change_id` bigint NULL DEFAULT NULL COMMENT '变更id',
  `schedule_end_time` datetime NULL DEFAULT NULL COMMENT '新结束时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `delay_files` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '延期文件',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1943241676180996098 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '变更延期记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_change_info
-- ----------------------------
DROP TABLE IF EXISTS `cm_change_info`;
CREATE TABLE `cm_change_info` (
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '变更标题',
  `requester` json NOT NULL COMMENT '申请人',
  `team_id` bigint DEFAULT NULL COMMENT '变更组ID',
  `team_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更组名',
  `is_urgent_change` tinyint DEFAULT NULL COMMENT '是否紧急变更（1-是 2-否）',
  `change_type` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更类型',
  `is_network_freeze_change` tinyint DEFAULT NULL COMMENT '是否封网变更（1-是 2-否）',
  `category_ids` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分类id',
  `category_name_list` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '分类名称',
  `application_ids` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '应用/系统id',
  `application_name` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '应用/系统名称',
  `location_id` bigint DEFAULT NULL COMMENT '地点id',
  `location_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地点名',
  `location_list` json DEFAULT NULL COMMENT '变更地点json',
  `src` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '相关SRC/MSP',
  `priority` tinyint DEFAULT NULL COMMENT '优先级(1-高 2-中 3-低)',
  `plan_time_start` datetime DEFAULT NULL COMMENT '计划开始时间',
  `plan_time_end` datetime DEFAULT NULL COMMENT '计划结束时间',
  `delay_end_time` datetime DEFAULT NULL COMMENT '延期结束时间',
  `temporary_url` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '临时访问链接',
  `change_reason` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更原因',
  `change_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '变更描述',
  `file_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件地址',
  `affected_description` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '影响描述',
  `affected_time` int DEFAULT NULL COMMENT '影响时间',
  `affected_time_type` tinyint DEFAULT NULL COMMENT '影响时间类型(1-分钟 2-小时)',
  `affected_user` varchar(5000) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '影响用户',
  `affected_application_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '影响系统ids',
  `affected_application_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '影响系统名称',
  `affected_device_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '影响设备ids',
  `affected_device_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '影响设备名',
  `notification_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知电邮地址',
  `notification_cc_email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '通知电邮抄送地址',
  `team_leader_list` json DEFAULT NULL COMMENT 'Team Leader List',
  `tester_list` json DEFAULT NULL COMMENT 'Tester List',
  `change_approver_list` json DEFAULT NULL COMMENT '变更审批人list',
  `service_delivery_team_list` json DEFAULT NULL COMMENT '服务提交团队list',
  `application_owner_list` json DEFAULT NULL COMMENT '系统所有人list',
  `change_implementer_list` json DEFAULT NULL COMMENT '变更实施人list',
  `change_verifier_list` json DEFAULT NULL COMMENT '变更验证人list',
  `change_owner_list` json DEFAULT NULL COMMENT '变更所有者list',
  `dept_leader_list` json DEFAULT NULL COMMENT '部门负责人list',
  `urgent_change_inspector_list` json DEFAULT NULL COMMENT '紧急变更核查人list',
  `systems_affected_no` int DEFAULT NULL COMMENT '受影响系统个数',
  `important_users` int DEFAULT NULL COMMENT '受影响重要用户',
  `fallback` int DEFAULT NULL COMMENT '回退/回滚',
  `complexity` int DEFAULT NULL COMMENT '变更复杂性/实施经验',
  `risk_level` tinyint DEFAULT NULL COMMENT '风险等级(1-低 2-中 3-高 4-不可接受)',
  `risk_score` int DEFAULT NULL COMMENT '风险得分',
  `request_doc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '需求文档',
  `request_doc_file_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '需求文档附件ids',
  `test_doc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '测试文档',
  `test_doc_file_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '测试档附件ids',
  `code_review` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '代码复查',
  `code_review_file_ids` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '代码复查附件ids',
  `roll_out_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '上线计划',
  `backout_plan` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '回退计划',
  `check_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '上线检查清单',
  `review_check_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '核查清单',
  `email_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '邮件内容',
  `special_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '特殊备注',
  `freeze_info` json DEFAULT NULL COMMENT '封网信息',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_start` (`plan_time_start`) USING BTREE,
  KEY `idx_eff_end` ((ifnull(`delay_end_time`,`plan_time_end`))) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1950387889116303363 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='变更申请表';
-- ----------------------------
-- Table structure for cm_change_item
-- ----------------------------
DROP TABLE IF EXISTS `cm_change_item`;
CREATE TABLE `cm_change_item`  (
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `change_id` bigint NULL DEFAULT NULL COMMENT '变更ID',
  `change_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更编号',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更标题',
  `stage` tinyint NULL DEFAULT NULL COMMENT '阶段',
  `processor_list` json NULL COMMENT '当前处理人List',
  `team_id` bigint NULL DEFAULT NULL COMMENT '变更组ID',
  `team_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更组名',
  `is_urgent_change` tinyint NULL DEFAULT NULL COMMENT '是否紧急变更（1-是 2-否）',
  `priority` tinyint NULL DEFAULT NULL COMMENT '优先级(1-高 2-中 3-低)',
  `instance_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程实例id',
  `variables` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT 'smartFlow variables',
  `complete_time` datetime NULL DEFAULT NULL COMMENT '完成时间',
  `submit_time` datetime NULL DEFAULT NULL COMMENT '提交时间',
  `requester` json NULL COMMENT '申请人',
  `is_network_freeze_change` tinyint NULL DEFAULT NULL COMMENT '是否封网变更（1-是 2-否）',
  `location_id` bigint NULL DEFAULT NULL COMMENT '地点id',
  `location_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地点名',
  `location_list` json NULL DEFAULT NULL COMMENT '变更地点json',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1943495553392361475 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '变更申请记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_change_record
-- ----------------------------
DROP TABLE IF EXISTS `cm_change_record`;
CREATE TABLE `cm_change_record` (
    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
    `create_by` bigint DEFAULT NULL COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `id` bigint NOT NULL AUTO_INCREMENT,
    `change_item_id` bigint DEFAULT NULL COMMENT '变更记录ID',
    `change_id` bigint DEFAULT NULL COMMENT '变更ID',
    `change_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更编号',
    `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更标题',
    `stage` tinyint DEFAULT NULL COMMENT '阶段',
    `processor_status` tinyint DEFAULT NULL COMMENT '审批状态',
    `process_order` int DEFAULT NULL COMMENT '顺序',
    `processor` json DEFAULT NULL COMMENT '当前处理人',
    `processor_time` datetime DEFAULT NULL COMMENT '审批时间',
    `team_id` bigint DEFAULT NULL COMMENT '变更组ID',
    `team_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更组名',
    `is_urgent_change` tinyint DEFAULT NULL COMMENT '是否紧急变更（1-是 2-否）',
    `priority` tinyint DEFAULT NULL COMMENT '优先级(1-高 2-中 3-低)',
    `opinion` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批意见/备注',
    `is_seal_addition` tinyint DEFAULT NULL COMMENT '是否需要加签（1-是 2-否）',
    `step` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更/回滚步骤',
    `task_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务id',
    `countersign_type` tinyint DEFAULT NULL COMMENT '会签类型（0-会签 1-或签）',
    `countersign` tinyint DEFAULT NULL COMMENT '是否允许加签（0-否 1-是）',
    `strategy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'smartFlow节点策略',
    `next_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'smartFlow待发送节点列表',
    `task_current` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'smartFlow当前任务信息',
    `is_network_freeze_change` tinyint DEFAULT NULL COMMENT '是否封网变更（1-是 2-否）',
    `requester` json DEFAULT NULL COMMENT '申请人',
    `location_id` bigint DEFAULT NULL COMMENT '地点id',
    `location_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '地点名',
    `location_list` json DEFAULT NULL COMMENT '变更地点json',
    `node_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '节点名称',
    `is_or_sign_processed` tinyint DEFAULT NULL COMMENT '或签是否已被处理（1-是 2-否）',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1950390326799654914 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='变更申请记录流水表';
-- ----------------------------
-- Table structure for cm_conf_info
-- ----------------------------
DROP TABLE IF EXISTS `cm_conf_info`;
CREATE TABLE `cm_conf_info`  (
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `type` tinyint NULL DEFAULT NULL COMMENT '类型（1-分类 2-封网地区 3- 变更地点 4-受影响设备 5-维护种类）',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `status` tinyint NULL DEFAULT NULL COMMENT '状态（1-可用 2-非可用)',
  `name_en` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '名称-英文',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1938054573088325635 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '配置信息' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_modify_log
-- ----------------------------
DROP TABLE IF EXISTS `cm_modify_log`;
CREATE TABLE `cm_modify_log`  (
  `id` bigint NOT NULL COMMENT '主键',
  `change_code` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更编号',
  `modify_filed` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改字段',
  `filed_name_zh` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改字段_zh',
  `filed_name_us` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改字段_us',
  `filed_name_tw` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '修改字段_tw',
  `content_old` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '修改内容-旧',
  `content_new` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '修改内容-新',
  `staff_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'StaffId',
  `staff_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'StaffName',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '变更修改记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_network_freeze_area
-- ----------------------------
DROP TABLE IF EXISTS `cm_network_freeze_area`;
CREATE TABLE `cm_network_freeze_area`  (
  `id` bigint NOT NULL COMMENT '主键',
  `freeze_id` bigint NULL DEFAULT NULL COMMENT '封网Id',
  `start_time` datetime NULL DEFAULT NULL COMMENT '封网开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '封网结束时间',
  `area_id` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '封网地区Id数组',
  `area_name` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封网地区',
  `area_name_en` varchar(1000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '封网地区-英文',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT = '封网时间地区' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_network_freeze_info
-- ----------------------------
DROP TABLE IF EXISTS `cm_network_freeze_info`;
CREATE TABLE `cm_network_freeze_info` (
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint DEFAULT NULL COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint DEFAULT NULL COMMENT '更新人',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封网名称',
  `level` tinyint NOT NULL COMMENT '封网等级',
  `freeze_apps` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '封禁应用',
  `file_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '附件ids',
  `is_attachment` tinyint DEFAULT NULL COMMENT '是否增加到邮件附件（1-是 2否）',
  `notification_email_to` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '通知电邮地址',
  `notification_email_cc` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '通知抄送电邮地址',
  `special_remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '特殊备注',
  `email_content` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '邮件内容',
  `requester` json DEFAULT NULL COMMENT '申请人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1950443691483148290 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='封网申请表';
-- ----------------------------
-- Table structure for cm_network_freeze_item
-- ----------------------------
DROP TABLE IF EXISTS `cm_network_freeze_item`;
CREATE TABLE `cm_network_freeze_item`  (
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `id` bigint NOT NULL AUTO_INCREMENT,
  `freeze_id` bigint NULL DEFAULT NULL COMMENT '变更ID',
  `freeze_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '变更编号',
  `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '封网名称',
  `level` tinyint NOT NULL COMMENT '封网等级',
  `stage` tinyint NULL DEFAULT NULL COMMENT '阶段',
  `processor_list` json NULL COMMENT '当前处理人List',
  `instance_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '流程实例id',
  `variables` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'smartFlow variables',
  `freeze_start_time` datetime NULL DEFAULT NULL COMMENT '封网开始时间',
  `freeze_end_time` datetime NULL DEFAULT NULL COMMENT '封网结束时间',
  `publish_time` datetime NULL DEFAULT NULL COMMENT '发布时间',
  `requester` json NULL DEFAULT NULL COMMENT '申请人',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 1942761014881701890 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '封网申请记录表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for cm_network_freeze_record
-- ----------------------------
DROP TABLE IF EXISTS `cm_network_freeze_record`;
CREATE TABLE `cm_network_freeze_record` (
    `create_dept` bigint DEFAULT NULL COMMENT '创建部门',
    `create_by` bigint DEFAULT NULL COMMENT '创建者',
    `create_time` datetime DEFAULT NULL COMMENT '创建时间',
    `update_by` bigint DEFAULT NULL COMMENT '更新人',
    `update_time` datetime DEFAULT NULL COMMENT '更新时间',
    `id` bigint NOT NULL AUTO_INCREMENT,
    `freeze_item_id` bigint DEFAULT NULL COMMENT '封网记录ID',
    `freeze_id` bigint DEFAULT NULL COMMENT '封网ID',
    `freeze_code` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '封网编号',
    `title` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '变更标题',
    `level` tinyint NOT NULL COMMENT '封网等级',
    `stage` tinyint DEFAULT NULL COMMENT '阶段',
    `processor_status` tinyint DEFAULT NULL COMMENT '审批状态',
    `processor` json DEFAULT NULL COMMENT '当前处理人',
    `processor_time` datetime DEFAULT NULL COMMENT '审批时间',
    `opinion` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '审批意见/备注',
    `process_order` int DEFAULT NULL COMMENT '顺序',
    `task_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '任务id',
    `countersign_type` tinyint DEFAULT NULL COMMENT '会签类型（0-会签 1-或签）',
    `strategy` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'smartFlow节点策略',
    `next_list` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'smartFlow待发送节点列表',
    `task_current` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT 'smartFlow当前任务信息',
    `node_name` varchar(100) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '节点名',
    PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1950443899659038723 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='封网申请记录流水表';
-- ----------------------------
-- Table structure for sys_client
-- ----------------------------
DROP TABLE IF EXISTS `sys_client`;
CREATE TABLE `sys_client`  (
  `id` bigint NOT NULL COMMENT 'id',
  `client_id` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户端id',
  `client_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户端key',
  `client_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '客户端秘钥',
  `grant_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授权类型',
  `device_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '设备类型',
  `active_timeout` int NULL DEFAULT 1800 COMMENT 'token活跃超时时间',
  `timeout` int NULL DEFAULT 604800 COMMENT 'token固定超时',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统授权表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_client
-- ----------------------------
INSERT INTO `sys_client` VALUES (1, 'e5cd7e4891bf95d1d19206ce24a7b32e', 'pc', 'pc123', 'password,social', 'pc', 1800, 43200, '0', '0', 103, 1, NOW(), 1, NULL);

-- ----------------------------
-- Table structure for sys_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_config`;
CREATE TABLE `sys_config`  (
  `config_id` bigint NOT NULL COMMENT '参数主键',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `config_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数名称',
  `config_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键名',
  `config_value` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '参数键值',
  `config_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '系统内置（Y是 N否）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '参数配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_config
-- ----------------------------
INSERT INTO `sys_config` VALUES (1, '000000', '主框架页-默认皮肤样式名称', 'sys.index.skinName', 'skin-blue', 'Y', 103, 1, NOW(), NULL, NULL, '蓝色 skin-blue、绿色 skin-green、紫色 skin-purple、红色 skin-red、黄色 skin-yellow');
INSERT INTO `sys_config` VALUES (2, '000000', '用户管理-账号初始密码', 'sys.user.initPassword', '*=VKtyCnJx2w', 'Y', 103, 1, NOW(), NULL, NULL, '初始化密码 123456');
INSERT INTO `sys_config` VALUES (3, '000000', '主框架页-侧边栏主题', 'sys.index.sideTheme', 'theme-dark', 'Y', 103, 1, NOW(), NULL, NULL, '深色主题theme-dark，浅色主题theme-light');
INSERT INTO `sys_config` VALUES (5, '000000', '账号自助-是否开启用户注册功能', 'sys.account.registerUser', 'false', 'Y', 103, 1, NOW(), NULL, NULL, '是否开启注册用户功能（true开启，false关闭）');
INSERT INTO `sys_config` VALUES (11, '000000', 'OSS预览列表资源开关', 'sys.oss.previewListResource', 'true', 'Y', 103, 1, NOW(), NULL, NULL, 'true:开启, false:关闭');

-- ----------------------------
-- Table structure for sys_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept`  (
  `dept_id` bigint NOT NULL COMMENT '部门id',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父部门id',
  `ancestors` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '祖级列表',
  `dept_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `dept_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '部门类别编码',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `leader` bigint NULL DEFAULT NULL COMMENT '负责人',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮箱',
  `description` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组描述',
  `email_sign` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '邮件签名',
  `dept_type` varchar(300) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '分组类型',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '部门状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '部门表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dept
-- ----------------------------
INSERT INTO `sys_dept` VALUES (100, '000000', 0, '0', 'CITIC TELECOM INTERNATIONAL', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', 103, 1, NOW(), NULL, NULL);
INSERT INTO `sys_dept` VALUES (1920403120731209729, '000000', 100, '0,100', 'Infra Team', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', 103, 1, NOW(), 1, NULL);
INSERT INTO `sys_dept` VALUES (1920403156194050049, '000000', 100, '0,100', 'APP Team', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', 103, 1, NOW(), 1, NULL);
INSERT INTO `sys_dept` VALUES (1920403189165473793, '000000', 100, '0,100', 'INRD Team', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', 103, 1, NOW(), 1, NULL);
INSERT INTO `sys_dept` VALUES (1923317788601065473, '000000', 100, '0,100', 'PMO Team', NULL, 0, NULL, NULL, NULL, NULL, NULL, NULL, '0', '0', 103, 1, NOW(), 1, NULL);

-- ----------------------------
-- Table structure for sys_dict_data
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_data`;
CREATE TABLE `sys_dict_data`  (
  `dict_code` bigint NOT NULL COMMENT '字典编码',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `dict_sort` int NULL DEFAULT 0 COMMENT '字典排序',
  `dict_label` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典标签',
  `dict_value` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典键值',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `css_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '样式属性（其他样式扩展）',
  `list_class` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '表格回显样式',
  `is_default` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否默认（Y是 N否）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_code`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典数据表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_data
-- ----------------------------
INSERT INTO `sys_dict_data` VALUES (1, '000000', 1, '男', '0', 'sys_user_sex', '', '', 'Y', 103, 1, NOW(), NULL, NULL, '性别男');
INSERT INTO `sys_dict_data` VALUES (2, '000000', 2, '女', '1', 'sys_user_sex', '', '', 'N', 103, 1, NOW(), NULL, NULL, '性别女');
INSERT INTO `sys_dict_data` VALUES (3, '000000', 3, '未知', '2', 'sys_user_sex', '', '', 'N', 103, 1, NOW(), NULL, NULL, '性别未知');
INSERT INTO `sys_dict_data` VALUES (4, '000000', 1, '显示', '0', 'sys_show_hide', '', 'primary', 'Y', 103, 1, NOW(), NULL, NULL, '显示菜单');
INSERT INTO `sys_dict_data` VALUES (5, '000000', 2, '隐藏', '1', 'sys_show_hide', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '隐藏菜单');
INSERT INTO `sys_dict_data` VALUES (6, '000000', 1, '正常', '0', 'sys_normal_disable', '', 'primary', 'Y', 103, 1, NOW(), NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (7, '000000', 2, '停用', '1', 'sys_normal_disable', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (12, '000000', 1, '是', 'Y', 'sys_yes_no', '', 'primary', 'Y', 103, 1, NOW(), NULL, NULL, '系统默认是');
INSERT INTO `sys_dict_data` VALUES (13, '000000', 2, '否', 'N', 'sys_yes_no', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '系统默认否');
INSERT INTO `sys_dict_data` VALUES (14, '000000', 1, '通知', '1', 'sys_notice_type', '', 'warning', 'Y', 103, 1, NOW(), NULL, NULL, '通知');
INSERT INTO `sys_dict_data` VALUES (15, '000000', 2, '公告', '2', 'sys_notice_type', '', 'success', 'N', 103, 1, NOW(), NULL, NULL, '公告');
INSERT INTO `sys_dict_data` VALUES (16, '000000', 1, '正常', '0', 'sys_notice_status', '', 'primary', 'Y', 103, 1, NOW(), NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (17, '000000', 2, '关闭', '1', 'sys_notice_status', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '关闭状态');
INSERT INTO `sys_dict_data` VALUES (18, '000000', 1, '新增', '1', 'sys_oper_type', '', 'info', 'N', 103, 1, NOW(), NULL, NULL, '新增操作');
INSERT INTO `sys_dict_data` VALUES (19, '000000', 2, '修改', '2', 'sys_oper_type', '', 'info', 'N', 103, 1, NOW(), NULL, NULL, '修改操作');
INSERT INTO `sys_dict_data` VALUES (20, '000000', 3, '删除', '3', 'sys_oper_type', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '删除操作');
INSERT INTO `sys_dict_data` VALUES (21, '000000', 4, '授权', '4', 'sys_oper_type', '', 'primary', 'N', 103, 1, NOW(), NULL, NULL, '授权操作');
INSERT INTO `sys_dict_data` VALUES (22, '000000', 5, '导出', '5', 'sys_oper_type', '', 'warning', 'N', 103, 1, NOW(), NULL, NULL, '导出操作');
INSERT INTO `sys_dict_data` VALUES (23, '000000', 6, '导入', '6', 'sys_oper_type', '', 'warning', 'N', 103, 1, NOW(), NULL, NULL, '导入操作');
INSERT INTO `sys_dict_data` VALUES (24, '000000', 7, '强退', '7', 'sys_oper_type', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '强退操作');
INSERT INTO `sys_dict_data` VALUES (25, '000000', 8, '生成代码', '8', 'sys_oper_type', '', 'warning', 'N', 103, 1, NOW(), NULL, NULL, '生成操作');
INSERT INTO `sys_dict_data` VALUES (26, '000000', 9, '清空数据', '9', 'sys_oper_type', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '清空操作');
INSERT INTO `sys_dict_data` VALUES (27, '000000', 1, '成功', '0', 'sys_common_status', '', 'primary', 'N', 103, 1, NOW(), NULL, NULL, '正常状态');
INSERT INTO `sys_dict_data` VALUES (28, '000000', 2, '失败', '1', 'sys_common_status', '', 'danger', 'N', 103, 1, NOW(), NULL, NULL, '停用状态');
INSERT INTO `sys_dict_data` VALUES (29, '000000', 99, '其他', '0', 'sys_oper_type', '', 'info', 'N', 103, 1, NOW(), NULL, NULL, '其他操作');
INSERT INTO `sys_dict_data` VALUES (30, '000000', 0, '密码认证', 'password', 'sys_grant_type', 'el-check-tag', 'default', 'N', 103, 1, NOW(), NULL, NULL, '密码认证');
INSERT INTO `sys_dict_data` VALUES (31, '000000', 0, '短信认证', 'sms', 'sys_grant_type', 'el-check-tag', 'default', 'N', 103, 1, NOW(), NULL, NULL, '短信认证');
INSERT INTO `sys_dict_data` VALUES (32, '000000', 0, '邮件认证', 'email', 'sys_grant_type', 'el-check-tag', 'default', 'N', 103, 1, NOW(), NULL, NULL, '邮件认证');
INSERT INTO `sys_dict_data` VALUES (33, '000000', 0, '小程序认证', 'xcx', 'sys_grant_type', 'el-check-tag', 'default', 'N', 103, 1, NOW(), NULL, NULL, '小程序认证');
INSERT INTO `sys_dict_data` VALUES (34, '000000', 0, '三方登录认证', 'social', 'sys_grant_type', 'el-check-tag', 'default', 'N', 103, 1, NOW(), NULL, NULL, '三方登录认证');
INSERT INTO `sys_dict_data` VALUES (35, '000000', 0, 'PC', 'pc', 'sys_device_type', '', 'default', 'N', 103, 1, NOW(), NULL, NULL, 'PC');
INSERT INTO `sys_dict_data` VALUES (36, '000000', 0, '安卓', 'android', 'sys_device_type', '', 'default', 'N', 103, 1, NOW(), NULL, NULL, '安卓');
INSERT INTO `sys_dict_data` VALUES (37, '000000', 0, 'iOS', 'ios', 'sys_device_type', '', 'default', 'N', 103, 1, NOW(), NULL, NULL, 'iOS');
INSERT INTO `sys_dict_data` VALUES (38, '000000', 0, '小程序', 'xcx', 'sys_device_type', '', 'default', 'N', 103, 1, NOW(), NULL, NULL, '小程序');

-- ----------------------------
-- Table structure for sys_dict_type
-- ----------------------------
DROP TABLE IF EXISTS `sys_dict_type`;
CREATE TABLE `sys_dict_type`  (
  `dict_id` bigint NOT NULL COMMENT '字典主键',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `dict_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典名称',
  `dict_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '字典类型',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`dict_id`) USING BTREE,
  UNIQUE INDEX `tenant_id`(`tenant_id` ASC, `dict_type` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '字典类型表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_dict_type
-- ----------------------------
INSERT INTO `sys_dict_type` VALUES (1, '000000', '用户性别', 'sys_user_sex', 103, 1, NOW(), NULL, NULL, '用户性别列表');
INSERT INTO `sys_dict_type` VALUES (2, '000000', '菜单状态', 'sys_show_hide', 103, 1, NOW(), NULL, NULL, '菜单状态列表');
INSERT INTO `sys_dict_type` VALUES (3, '000000', '系统开关', 'sys_normal_disable', 103, 1, NOW(), NULL, NULL, '系统开关列表');
INSERT INTO `sys_dict_type` VALUES (6, '000000', '系统是否', 'sys_yes_no', 103, 1, NOW(), NULL, NULL, '系统是否列表');
INSERT INTO `sys_dict_type` VALUES (7, '000000', '通知类型', 'sys_notice_type', 103, 1, NOW(), NULL, NULL, '通知类型列表');
INSERT INTO `sys_dict_type` VALUES (8, '000000', '通知状态', 'sys_notice_status', 103, 1, NOW(), NULL, NULL, '通知状态列表');
INSERT INTO `sys_dict_type` VALUES (9, '000000', '操作类型', 'sys_oper_type', 103, 1, NOW(), NULL, NULL, '操作类型列表');
INSERT INTO `sys_dict_type` VALUES (10, '000000', '系统状态', 'sys_common_status', 103, 1, NOW(), NULL, NULL, '登录状态列表');
INSERT INTO `sys_dict_type` VALUES (11, '000000', '授权类型', 'sys_grant_type', 103, 1, NOW(), NULL, NULL, '认证授权类型');
INSERT INTO `sys_dict_type` VALUES (12, '000000', '设备类型', 'sys_device_type', 103, 1, NOW(), NULL, NULL, '客户端设备类型');

-- ----------------------------
-- Table structure for sys_file_upload
-- ----------------------------
DROP TABLE IF EXISTS `sys_file_upload`;
CREATE TABLE `sys_file_upload`  (
  `file_id` bigint NOT NULL COMMENT '文件主键ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户编号',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件名',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原始文件名',
  `file_suffix` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件后缀名',
  `file_size` bigint NULL DEFAULT 0 COMMENT '文件大小（字节）',
  `file_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '文件路径',
  `storage_path` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '存储路径',
  `directory_date` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '子目录日期(格式：yyyy-MM-dd)',
  `content_type` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '文件类型',
  `download_count` int NULL DEFAULT 0 COMMENT '下载次数',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`file_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '文件上传下载表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_logininfor
-- ----------------------------
DROP TABLE IF EXISTS `sys_logininfor`;
CREATE TABLE `sys_logininfor`  (
  `info_id` bigint NOT NULL COMMENT '访问ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `user_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户账号',
  `client_key` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '客户端',
  `device_type` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '设备类型',
  `ipaddr` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录IP地址',
  `login_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '登录地点',
  `browser` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '浏览器类型',
  `os` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作系统',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '登录状态（0成功 1失败）',
  `msg` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '提示消息',
  `login_time` datetime NULL DEFAULT NULL COMMENT '访问时间',
  PRIMARY KEY (`info_id`) USING BTREE,
  INDEX `idx_sys_logininfor_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_logininfor_lt`(`login_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '系统访问记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_menu`;
CREATE TABLE `sys_menu`  (
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  `menu_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '菜单名称',
  `parent_id` bigint NULL DEFAULT 0 COMMENT '父菜单ID',
  `order_num` int NULL DEFAULT 0 COMMENT '显示顺序',
  `path` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '路由地址',
  `component` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '组件路径',
  `query_param` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '路由参数',
  `is_frame` int NULL DEFAULT 1 COMMENT '是否为外链（0是 1否）',
  `is_cache` int NULL DEFAULT 0 COMMENT '是否缓存（0缓存 1不缓存）',
  `menu_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '菜单类型（M目录 C菜单 F按钮）',
  `visible` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '显示状态（0显示 1隐藏）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '菜单状态（0正常 1停用）',
  `perms` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '权限标识',
  `icon` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '#' COMMENT '菜单图标',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '备注',
  PRIMARY KEY (`menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '菜单权限表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_menu
-- ----------------------------
INSERT INTO `sys_menu` VALUES (1922225161931378690, '系统设置', 0, 18, 'system_settings', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'system', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1922226241276792834, '配置管理', 1922225161931378690, 4, 'config', 'systemSettings/configMgt/index', '', 1, 0, 'C', '0', '0', NULL, 'table', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924743384491405314, '首页', 0, 11, 'home/index', 'home/index', NULL, 1, 1, 'C', '0', '0', '', 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924744101327319041, '封网信息查看', 1924743384491405314, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:dashboard:networkFreeze', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924746237876416513, '我的审批', 0, 12, 'approval', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924746524620009473, '变更审批', 1924746237876416513, 1, 'change', 'changeManagement/approval', NULL, 1, 0, 'C', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924746812303126530, '封网审批', 1924746237876416513, 2, 'freeze', 'networkFreezeManagement/approval', NULL, 1, 0, 'C', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924746992570118146, '变更管理', 0, 13, 'change', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924747199278002177, '变更申请', 1924746992570118146, 1, 'application', 'changeManagement/application', NULL, 1, 0, 'C', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924747439355768834, '新增变更（含编辑/复制/提交）', 1924747199278002177, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:edit', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924747650916462594, '变更记录', 1924746992570118146, 2, 'record', 'changeManagement/index', NULL, 1, 0, 'C', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924750446722068482, '封网管理', 0, 14, 'network_freeze', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924751673455325186, '封网申请', 1924750446722068482, 1, 'application', 'networkFreezeManagement/application', NULL, 1, 0, 'C', '0', '0', '', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924751806108577794, '新增封网（含编辑/复制/提交）', 1924751673455325186, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:edit', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924751966356156417, '封网记录', 1924750446722068482, 2, 'record', 'networkFreezeManagement/index', NULL, 1, 0, 'C', '0', '0', '', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924752151866028033, '列表信息', 1924751966356156417, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924752195914608642, '列表查询', 1924751966356156417, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924752316857364481, '字段展示', 1924751966356156417, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:filed', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924752396045824002, '封网详情（含导出）', 1924751966356156417, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:detail', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924753598863159298, '应用管理', 0, 15, 'app', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924753798436532226, '应用设置', 1924753598863159298, 1, 'setting', 'appManagement/index', NULL, 1, 0, 'C', '0', '0', NULL, '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924753863649570818, '列表信息', 1924753798436532226, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:applicationManage:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924753921283502082, '列表查询', 1924753798436532226, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:applicationManage:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924753962584813570, '应用详情', 1924753798436532226, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:applicationManage:detail', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924754208400388098, '变更统计', 0, 16, 'statistics/index', 'statistics/index', NULL, 1, 0, 'C', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924754601406672898, '列表信息', 1924754208400388098, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeStatistics:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924754664417701889, '查询', 1924754208400388098, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeStatistics:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924754718222233601, '导出', 1924754208400388098, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeStatistics:export', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924754866658652161, '日志管理', 0, 17, 'log', NULL, NULL, 1, 0, 'M', '0', '0', NULL, 'example', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755014755332098, '用户日志', 1924754866658652161, 1, 'opt', 'logManagement/opt_record', NULL, 1, 0, 'C', '0', '0', NULL, '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755089082593282, '列表信息', 1924755014755332098, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'monitor:modifyLog:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755179172048898, '列表查询', 1924755014755332098, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'monitor:modifyLog:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755335799943170, '管理日志', 1924754866658652161, 2, 'mgt', 'logManagement/mgt_record', NULL, 1, 0, 'C', '0', '0', NULL, '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755404435533826, '列表信息', 1924755335799943170, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'monitor:operLog:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755455752843266, '列表查询', 1924755335799943170, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'monitor:operLog:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755715011162114, '账号管理', 1922225161931378690, 1, 'account', 'systemSettings/accountMgt/index', NULL, 1, 0, 'C', '0', '0', '', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755802802139137, '列表信息', 1924755715011162114, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755855650369538, '列表查询', 1924755715011162114, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924755922226556929, '账号详情', 1924755715011162114, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:detail', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756022839521281, '分组管理', 1922225161931378690, 2, 'group', 'systemSettings/groupMgt/index', NULL, 1, 0, 'C', '0', '0', NULL, '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756161025060865, '列表信息', 1924756022839521281, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756214649237505, '列表查询', 1924756022839521281, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756264444014593, '分组详情', 1924756022839521281, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:detail', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756362485870594, '角色管理', 1922225161931378690, 3, 'role', 'systemSettings/roleMgt/index', NULL, 1, 0, 'C', '0', '0', NULL, '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756431557668865, '列表信息', 1924756362485870594, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:list', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756467184087041, '角色详情', 1924756362485870594, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:detail', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1924756506849619970, '列表查询', 1924756362485870594, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:query', '', 103, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934880908590764034, '便捷导航-新增变更', 1924743384491405314, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:add', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934881402662998017, '便捷导航-历史变更', 1924743384491405314, 3, 'business:changeInfo:list', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934882449674522625, '便捷导航-新增封网', 1924743384491405314, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:add', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934882572844453889, '便捷导航-历史封网', 1924743384491405314, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeInfo:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934882741749075970, '未完结变更', 1924743384491405314, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:dashboard:changeUnFinished', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934882824288784385, '待办列表', 1924743384491405314, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:dashboard:changeTodo', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934882977204719618, '日历筛选', 1924743384491405314, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:dashboard:calendar', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934883088991309825, '日历查看', 1924743384491405314, 9, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:dashboard:dailyChangeList', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934883587832467457, '列表信息', 1924746524620009473, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeRecord:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934883680526585857, '列表查询', 1924746524620009473, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeRecord:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934883860298649601, '字段展示', 1924746524620009473, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeRecord:filed', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934883968864014338, '审批操作', 1924746524620009473, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeRecord:update', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934884082210885634, '列表信息', 1924746812303126530, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeRecord:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934884172304535553, '列表查询', 1924746812303126530, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeRecord:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934884313782603778, '字段展示', 1924746812303126530, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeRecord:filed', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934884430627524609, '审批操作', 1924746812303126530, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkFreezeRecord:update', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934885025803456514, '列表信息', 1924747650916462594, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934885169152184322, '列表查询', 1924747650916462594, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934885289721647106, '字段展示', 1924747650916462594, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:filed', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934885383799885825, '变更详情（含导出）', 1924747650916462594, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeInfo:detail', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934888259779612673, '新增应用（含编辑/删除）', 1924753798436532226, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:applicationManage:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934889986876559362, '新增账号（含编辑/删除）', 1924755715011162114, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:user:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934890533369204738, '新增分组（含编辑/删除）', 1924756022839521281, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:dept:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934891386222202881, '新增角色（含编辑/删除）', 1924756362485870594, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'system:role:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934891774115631106, '应用分类-列表信息', 1922226241276792834, 1, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:classification:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934891979481337857, '应用分类-列表查询', 1922226241276792834, 2, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:classification:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934892130182680578, '应用分类-详情', 1922226241276792834, 3, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:classification:detail', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934892279046918146, '应用分类-新增（含编辑/删除）', 1922226241276792834, 4, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:classification:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934892446118629377, '变更地点-列表查询', 1922226241276792834, 6, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeLocation:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934892688746532865, '变更地点-列表信息', 1922226241276792834, 5, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeLocation:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934893257158610945, '变更地点-详情', 1922226241276792834, 7, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeLocation:detail', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934893389509873666, '变更地点-新增（含编辑/删除）', 1922226241276792834, 8, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:changeLocation:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934893601716490242, '受影响设备-列表信息', 1922226241276792834, 9, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:affectedDevice:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934893715042390017, '受影响设备-列表查询', 1922226241276792834, 10, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:affectedDevice:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934893858399506433, '受影响设备-详情', 1922226241276792834, 11, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:affectedDevice:detail', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894002884890626, '受影响设备-新增（含编辑/删除）', 1922226241276792834, 12, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:affectedDevice:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894134716059649, '封网地区-列表信息', 1922226241276792834, 13, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkBlockingArea:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894254710902785, '封网地区-列表查询', 1922226241276792834, 14, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkBlockingArea:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894407106744321, '封网地区-详情', 1922226241276792834, 15, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkBlockingArea:detail', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894563914993666, '封网地区-新增（含编辑/删除）', 1922226241276792834, 16, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:networkBlockingArea:edit', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894689425346562, '维护种类-列表信息', 1922226241276792834, 17, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:maintenanceKind:list', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894805364297729, '维护种类-列表查询', 1922226241276792834, 18, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:maintenanceKind:query', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934894898037444610, '维护种类-详情', 1922226241276792834, 19, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:maintenanceKind:detail', '', NULL, 1, NOW(), 1, NULL, '');
INSERT INTO `sys_menu` VALUES (1934895003889094657, '维护种类-新增（含编辑/删除）', 1922226241276792834, 20, '', NULL, NULL, 1, 0, 'F', '0', '0', 'business:maintenanceKind:edit', '', NULL, 1, NOW(), 1, NULL, '');

-- ----------------------------
-- Table structure for sys_notice
-- ----------------------------
DROP TABLE IF EXISTS `sys_notice`;
CREATE TABLE `sys_notice`  (
  `notice_id` bigint NOT NULL COMMENT '公告ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `notice_title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告标题',
  `notice_type` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '公告类型（1通知 2公告）',
  `notice_content` longblob NULL COMMENT '公告内容',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '公告状态（0正常 1关闭）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`notice_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '通知公告表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_oper_log
-- ----------------------------
DROP TABLE IF EXISTS `sys_oper_log`;
CREATE TABLE `sys_oper_log`  (
  `oper_id` bigint NOT NULL COMMENT '日志主键',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `title` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '模块标题',
  `business_type` int NULL DEFAULT 0 COMMENT '业务类型（0其它 1新增 2修改 3删除）',
  `method` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '方法名称',
  `request_method` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求方式',
  `operator_type` int NULL DEFAULT 0 COMMENT '操作类别（0其它 1后台用户 2手机端用户）',
  `oper_name` json NULL COMMENT '操作人员',
  `dept_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '部门名称',
  `oper_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '请求URL',
  `oper_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '主机地址',
  `oper_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '操作地点',
  `oper_param_original` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `oper_param` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '请求参数',
  `json_result` mediumtext CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '返回参数',
  `status` int NULL DEFAULT 0 COMMENT '操作状态（0正常 1异常）',
  `error_msg` varchar(4000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '错误消息',
  `oper_time` datetime NULL DEFAULT NULL COMMENT '操作时间',
  `cost_time` bigint NULL DEFAULT 0 COMMENT '消耗时间',
  PRIMARY KEY (`oper_id`) USING BTREE,
  INDEX `idx_sys_oper_log_bt`(`business_type` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_s`(`status` ASC) USING BTREE,
  INDEX `idx_sys_oper_log_ot`(`oper_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '操作日志记录' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_oss
-- ----------------------------
DROP TABLE IF EXISTS `sys_oss`;
CREATE TABLE `sys_oss`  (
  `oss_id` bigint NOT NULL COMMENT '对象存储主键',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件名',
  `original_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '原名',
  `file_suffix` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '文件后缀名',
  `url` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'URL地址',
  `ext1` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL COMMENT '扩展字段',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `create_by` bigint NULL DEFAULT NULL COMMENT '上传人',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新人',
  `service` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT 'minio' COMMENT '服务商',
  PRIMARY KEY (`oss_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = 'OSS对象存储表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_oss_config
-- ----------------------------
DROP TABLE IF EXISTS `sys_oss_config`;
CREATE TABLE `sys_oss_config`  (
  `oss_config_id` bigint NOT NULL COMMENT '主键',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `config_key` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '配置key',
  `access_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT 'accessKey',
  `secret_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '秘钥',
  `bucket_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '桶名称',
  `prefix` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '前缀',
  `endpoint` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '访问站点',
  `domain` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '自定义域名',
  `is_https` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'N' COMMENT '是否https（Y=是,N=否）',
  `region` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '域',
  `access_policy` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '1' COMMENT '桶权限类型(0=private 1=public 2=custom)',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '是否默认（0=是,1=否）',
  `ext1` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '扩展字段',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`oss_config_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '对象存储配置表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_oss_config
-- ----------------------------

-- ----------------------------
-- Table structure for sys_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_post`;
CREATE TABLE `sys_post`  (
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `dept_id` bigint NOT NULL COMMENT '部门id',
  `post_code` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位编码',
  `post_category` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '岗位类别编码',
  `post_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '岗位名称',
  `post_sort` int NOT NULL COMMENT '显示顺序',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '状态（0正常 1停用）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '岗位信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Table structure for sys_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_role`;
CREATE TABLE `sys_role`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `role_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色名称',
  `role_key` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色权限字符串',
  `role_sort` int NOT NULL COMMENT '显示顺序',
  `data_scope` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '1' COMMENT '数据范围（1：全部数据权限 2：自定数据权限 3：本部门数据权限 4：本部门及以下数据权限 5：仅本人数据权限 6：部门及以下或本人数据权限）',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `dept_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '部门树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '角色状态（0正常 1停用）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '描述',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role
-- ----------------------------
INSERT INTO `sys_role` VALUES (1, '000000', '超级管理员', 'superadmin', 1, '1', 1, 1, '0', NULL, '0', 103, 1, NOW(), NULL, NULL, '超级管理员');
INSERT INTO `sys_role` VALUES (1922548055773282306, '000000', '普通员工', 'common user', 1, '1', 1, 1, '0', 'common user', '0', 103, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927898468567793665, '000000', 'TeamLeader', 'bOiAS3dS', 1, '1', 1, 1, '0', 'TeamLeader', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927898750571823106, '000000', 'TeamManager', 'hoN3zJOW', 1, '1', 1, 1, '0', 'TeamManager', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903203362390018, '000000', 'FunctionHead', 'sSfPtPyd', 1, '1', 1, 1, '0', 'FunctionHead', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903567016935426, '000000', 'DeptHead', 'EaJKMZCQ', 1, '1', 1, 1, '0', 'DeptHead', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903606795714561, '000000', 'Service Delivery Team', 'nZNwrvur', 1, '1', 1, 1, '0', 'Service Delivery Team', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903650508750850, '000000', 'PMO Handler', 'MLeKhK74', 1, '1', 1, 1, '0', 'PMO Handler', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903683119464449, '000000', 'Auditor', '5nKWNhJC', 1, '1', 1, 1, '0', 'Auditor', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903709157703681, '000000', 'Reviewer', 'FwECkrGb', 1, '1', 1, 1, '0', 'Reviewer1', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1927903738484277249, '000000', 'Implementer', '2KHVkoRw', 1, '1', 1, 1, '0', 'Implementer', '0', 7, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1933022110981050369, '000000', 'PMO DeptHead', 'hsjk1w', 1, '1', 1, 1, '0', 'PMO DeptHead', '0', 103, 1, NOW(), 1, NULL, NULL);
INSERT INTO `sys_role` VALUES (1934910954080681986, '000000', 'Admin', 'Admin', 1, '1', 1, 1, '0', NULL, '0', NULL, 1, NOW(), 1, NULL, '');

-- ----------------------------
-- Table structure for sys_role_dept
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_dept`;
CREATE TABLE `sys_role_dept`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `dept_id` bigint NOT NULL COMMENT '部门ID',
  PRIMARY KEY (`role_id`, `dept_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和部门关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_dept
-- ----------------------------

-- ----------------------------
-- Table structure for sys_role_menu
-- ----------------------------
DROP TABLE IF EXISTS `sys_role_menu`;
CREATE TABLE `sys_role_menu`  (
  `role_id` bigint NOT NULL COMMENT '角色ID',
  `menu_id` bigint NOT NULL COMMENT '菜单ID',
  PRIMARY KEY (`role_id`, `menu_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '角色和菜单关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_role_menu
-- ----------------------------
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1922226241276792834);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924747199278002177);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924747439355768834);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924753598863159298);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924753798436532226);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924753863649570818);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924753921283502082);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924753962584813570);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934880908590764034);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934882741749075970);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934891774115631106);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934891979481337857);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934892130182680578);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934892446118629377);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934892688746532865);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934893257158610945);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934893601716490242);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934893715042390017);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934893858399506433);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934894689425346562);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934894805364297729);
INSERT INTO `sys_role_menu` VALUES (1922548055773282306, 1934894898037444610);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924753598863159298);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924753798436532226);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924753863649570818);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924753921283502082);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924753962584813570);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924754866658652161);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755014755332098);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755089082593282);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755179172048898);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927898468567793665, 1934888259779612673);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924754866658652161);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755014755332098);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755089082593282);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755179172048898);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927898750571823106, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927903203362390018, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 101);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924750446722068482);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924751966356156417);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924752151866028033);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924752195914608642);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924752316857364481);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924752396045824002);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924753598863159298);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924753798436532226);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924753863649570818);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924753921283502082);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924753962584813570);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927903567016935426, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927903606795714561, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1922226241276792834);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924750446722068482);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924751673455325186);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924751806108577794);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924751966356156417);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924752151866028033);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924752195914608642);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924752316857364481);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924752396045824002);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924753598863159298);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924753798436532226);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924753863649570818);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924753921283502082);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924753962584813570);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934882449674522625);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934882572844453889);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934891774115631106);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934891979481337857);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934892130182680578);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934893601716490242);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934893715042390017);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934893858399506433);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934894134716059649);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934894254710902785);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934894407106744321);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934894689425346562);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934894805364297729);
INSERT INTO `sys_role_menu` VALUES (1927903650508750850, 1934894898037444610);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924750446722068482);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924751966356156417);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924752151866028033);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924752195914608642);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924752316857364481);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924752396045824002);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924754866658652161);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755014755332098);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755089082593282);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755179172048898);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755335799943170);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755404435533826);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755455752843266);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934882572844453889);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927903683119464449, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927903709157703681, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924746524620009473);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934882824288784385);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934883587832467457);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934883680526585857);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934883860298649601);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934883968864014338);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1927903738484277249, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1922226241276792834);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924746237876416513);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924746812303126530);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924750446722068482);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924751966356156417);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924752151866028033);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924752195914608642);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924752316857364481);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924752396045824002);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924753598863159298);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924753798436532226);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924753863649570818);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924753921283502082);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924753962584813570);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934882572844453889);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934884082210885634);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934884172304535553);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934884313782603778);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934884430627524609);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934893601716490242);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934893715042390017);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934893858399506433);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934894134716059649);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934894254710902785);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934894407106744321);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934894689425346562);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934894805364297729);
INSERT INTO `sys_role_menu` VALUES (1933022110981050369, 1934894898037444610);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1922226241276792834);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934889986876559362);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934890533369204738);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934891386222202881);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934891774115631106);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934891979481337857);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934892130182680578);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934892279046918146);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934892446118629377);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934892688746532865);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934893257158610945);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934893389509873666);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934893601716490242);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934893715042390017);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934893858399506433);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894002884890626);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894134716059649);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894254710902785);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894407106744321);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894563914993666);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894689425346562);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894805364297729);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934894898037444610);
INSERT INTO `sys_role_menu` VALUES (1934910954080681986, 1934895003889094657);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1922225161931378690);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1922226241276792834);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924746992570118146);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924747650916462594);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924750446722068482);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924751966356156417);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924752151866028033);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924752195914608642);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924752316857364481);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924752396045824002);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924753598863159298);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924753798436532226);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924753863649570818);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924753921283502082);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924753962584813570);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924754208400388098);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924754601406672898);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924754664417701889);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924754718222233601);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924754866658652161);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755014755332098);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755089082593282);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755179172048898);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755335799943170);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755404435533826);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755455752843266);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755715011162114);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755802802139137);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755855650369538);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924755922226556929);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756022839521281);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756161025060865);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756214649237505);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756264444014593);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756362485870594);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756431557668865);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756467184087041);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1924756506849619970);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934881402662998017);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934882572844453889);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934882977204719618);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934883088991309825);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934885025803456514);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934885169152184322);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934885289721647106);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934885383799885825);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934891774115631106);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934891979481337857);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934892130182680578);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934892446118629377);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934892688746532865);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934893257158610945);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934893601716490242);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934893715042390017);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934893858399506433);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934894134716059649);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934894254710902785);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934894407106744321);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934894689425346562);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934894805364297729);
INSERT INTO `sys_role_menu` VALUES (1937702496289959938, 1934894898037444610);
INSERT INTO `sys_role_menu` VALUES (1937703872801812481, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1937703872801812481, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1938044990995357697, 1924743384491405314);
INSERT INTO `sys_role_menu` VALUES (1938044990995357697, 1924744101327319041);
INSERT INTO `sys_role_menu` VALUES (1938044990995357697, 1934881402662998017);

-- ----------------------------
-- Table structure for sys_social
-- ----------------------------
DROP TABLE IF EXISTS `sys_social`;
CREATE TABLE `sys_social`  (
  `id` bigint NOT NULL COMMENT '主键',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户id',
  `auth_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '平台+平台唯一id',
  `source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户来源',
  `open_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台编号唯一id',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '登录账号',
  `nick_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户昵称',
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `avatar` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '头像地址',
  `access_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户的授权令牌',
  `expire_in` int NULL DEFAULT NULL COMMENT '用户的授权令牌的有效期，部分平台可能没有',
  `refresh_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '刷新令牌，部分平台可能没有',
  `access_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '平台的授权信息，部分平台可能没有',
  `union_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户的 unionid',
  `scope` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '授予的权限，部分平台可能没有',
  `token_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '个别平台的授权信息，部分平台可能没有',
  `id_token` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'id token，部分平台可能没有',
  `mac_algorithm` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小米平台用户的附带属性，部分平台可能没有',
  `mac_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '小米平台用户的附带属性，部分平台可能没有',
  `code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户的授权code，部分平台可能没有',
  `oauth_token` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Twitter平台用户的附带属性，部分平台可能没有',
  `oauth_token_secret` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT 'Twitter平台用户的附带属性，部分平台可能没有',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '社会化关系表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_social
-- ----------------------------

-- ----------------------------
-- Table structure for sys_tenant
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant`;
CREATE TABLE `sys_tenant`  (
  `id` bigint NOT NULL COMMENT 'id',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '租户编号',
  `contact_user_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系人',
  `contact_phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '联系电话',
  `company_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业名称',
  `license_number` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '统一社会信用代码',
  `address` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '地址',
  `intro` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '企业简介',
  `domain` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '域名',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `package_id` bigint NULL DEFAULT NULL COMMENT '租户套餐编号',
  `expire_time` datetime NULL DEFAULT NULL COMMENT '过期时间',
  `account_count` int NULL DEFAULT -1 COMMENT '用户数量（-1不限制）',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '租户状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_tenant
-- ----------------------------
INSERT INTO `sys_tenant` VALUES (1, '000000', '管理组', '***********', 'XXX有限公司', NULL, NULL, '多租户通用后台管理管理系统', NULL, NULL, NULL, NULL, -1, '0', '0', 103, 1, NOW(), NULL, NULL);

-- ----------------------------
-- Table structure for sys_tenant_package
-- ----------------------------
DROP TABLE IF EXISTS `sys_tenant_package`;
CREATE TABLE `sys_tenant_package`  (
  `package_id` bigint NOT NULL COMMENT '租户套餐id',
  `package_name` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '套餐名称',
  `menu_ids` varchar(3000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '关联菜单id',
  `remark` varchar(200) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `menu_check_strictly` tinyint(1) NULL DEFAULT 1 COMMENT '菜单树选择项是否关联显示',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `del_flag` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`package_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '租户套餐表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_tenant_package
-- ----------------------------

-- ----------------------------
-- Table structure for sys_user
-- ----------------------------
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `staff_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'StaffId',
  `staff_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT 'StaffName',
  `tenant_id` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '000000' COMMENT '租户编号',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `user_name` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '用户账号',
  `user_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT 'sys_user' COMMENT '用户类型（sys_user系统用户）',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `phonenumber` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '用户性别（0男 1女 2未知）',
  `avatar` bigint NULL DEFAULT NULL COMMENT '头像地址',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '密码',
  `status` char(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '帐号状态（1正常 0停用）',
  `team_leader` json NULL COMMENT '对应Team leader',
  `team_manager` json NULL COMMENT '对应Team Manager',
  `direct_leader` json NULL COMMENT '直属领导',
  `create_type` tinyint NULL DEFAULT NULL COMMENT '创建方式（0-新增 1-excel导入）',
  `sync_status` tinyint NULL DEFAULT NULL COMMENT '同步状态 （1-同步成功）',
  `del_flag` varchar(1) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '0' COMMENT '删除标志（0代表存在 1代表删除）',
  `login_ip` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户信息表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user
-- ----------------------------
INSERT INTO `sys_user` VALUES (1, 'admin', 'admin', '000000', NULL, 'admin', 'sys_user', NULL, NULL, '1', NULL, '$2a$10$7JB720yubVSZvUI0rEqK/.VqGOZTH.ulu33dHOiBE8ByOhJIrdAu2', '1', NULL, NULL, NULL, NULL, NULL, '0', NULL, NULL, NULL, NULL, NOW(), NULL, NULL, '管理员');

-- ----------------------------
-- Table structure for sys_user_post
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_post`;
CREATE TABLE `sys_user_post`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `post_id` bigint NOT NULL COMMENT '岗位ID',
  PRIMARY KEY (`user_id`, `post_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户与岗位关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_post
-- ----------------------------
INSERT INTO `sys_user_post` VALUES (1, 1);

-- ----------------------------
-- Table structure for sys_user_role
-- ----------------------------
DROP TABLE IF EXISTS `sys_user_role`;
CREATE TABLE `sys_user_role`  (
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `role_id` bigint NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '用户和角色关联表' ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of sys_user_role
-- ----------------------------
INSERT INTO `sys_user_role` VALUES (1, 1);

SET FOREIGN_KEY_CHECKS = 1;
