package com.cec.common.excel.handler;

import com.alibaba.excel.metadata.Head;
import com.alibaba.excel.metadata.data.WriteCellData;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteTableHolder;
import com.alibaba.excel.write.style.AbstractCellStyleStrategy;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;

import java.util.List;

/**
 * 自动换行样式策略
 * 为所有单元格设置自动换行，并根据内容调整行高
 *
 * <AUTHOR>
 */
public class AutoWrapStyleStrategy extends AbstractCellStyleStrategy {

    // 默认行高（以磅为单位）
    private static final short DEFAULT_ROW_HEIGHT = 15;
    
    // 最大行高（以磅为单位）
    private static final short MAX_ROW_HEIGHT = 200;
    
    // 每行字符数估算（用于计算行高）
    private static final int CHARS_PER_LINE = 50;

    @Override
    protected void setHeadCellStyle(Cell cell, Head head, Integer relativeRowIndex) {
        // 设置表头样式
        setAutoWrapStyle(cell, true);
    }

    @Override
    protected void setContentCellStyle(Cell cell, Head head, Integer relativeRowIndex) {
        // 设置内容样式
        setAutoWrapStyle(cell, false);
    }

    /**
     * 设置自动换行样式
     *
     * @param cell   单元格
     * @param isHead 是否为表头
     */
    private void setAutoWrapStyle(Cell cell, boolean isHead) {
        Workbook workbook = cell.getSheet().getWorkbook();
        CellStyle cellStyle = workbook.createCellStyle();
        
        // 设置自动换行
        cellStyle.setWrapText(true);
        
        if (isHead) {
            // 表头样式：加粗、居中
            cellStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.CENTER);
            cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.CENTER);
        } else {
            // 内容样式：左对齐、顶部对齐
            cellStyle.setAlignment(org.apache.poi.ss.usermodel.HorizontalAlignment.LEFT);
            cellStyle.setVerticalAlignment(org.apache.poi.ss.usermodel.VerticalAlignment.TOP);
        }
        
        cell.setCellStyle(cellStyle);
        
        // 根据内容调整行高
        adjustRowHeight(cell);
    }

    /**
     * 根据单元格内容调整行高
     *
     * @param cell 单元格
     */
    private void adjustRowHeight(Cell cell) {
        Row row = cell.getRow();
        String cellValue = getCellValue(cell);
        
        if (cellValue != null && !cellValue.isEmpty()) {
            // 计算需要的行数
            int lines = calculateLines(cellValue);
            
            // 计算行高（每行约15磅）
            short newHeight = (short) (DEFAULT_ROW_HEIGHT * lines);
            
            // 限制最大行高
            newHeight = (short) Math.min(newHeight, MAX_ROW_HEIGHT);
            
            // 如果新行高大于当前行高，则更新
            if (newHeight > row.getHeight()) {
                row.setHeight(newHeight);
            }
        }
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                return String.valueOf(cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }

    /**
     * 计算文本需要的行数
     *
     * @param text 文本内容
     * @return 行数
     */
    private int calculateLines(String text) {
        if (text == null || text.isEmpty()) {
            return 1;
        }
        
        // 按换行符分割
        String[] lines = text.split("\\r?\\n");
        int totalLines = 0;
        
        for (String line : lines) {
            // 计算每行因为长度需要换行的次数
            int lineLength = getDisplayLength(line);
            int wrappedLines = Math.max(1, (int) Math.ceil((double) lineLength / CHARS_PER_LINE));
            totalLines += wrappedLines;
        }
        
        return Math.max(1, totalLines);
    }

    /**
     * 计算字符串的显示长度（中文字符算2个长度）
     *
     * @param text 文本
     * @return 显示长度
     */
    private int getDisplayLength(String text) {
        if (text == null) {
            return 0;
        }
        
        int length = 0;
        for (char c : text.toCharArray()) {
            // 中文和特殊字符长度算2，ASCII字符算1
            if (c > 256) {
                length += 2;
            } else {
                length += 1;
            }
        }
        return length;
    }
}
