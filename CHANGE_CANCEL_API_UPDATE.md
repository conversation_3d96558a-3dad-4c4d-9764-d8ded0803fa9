# 变更取消接口更新说明

## 修改概述

将变更取消接口从只接受路径参数改为接受请求体参数，以支持传递取消原因代码和描述。

## 修改内容

### 1. 新增请求DTO

**文件**: `changeManagement-modules/changeManagement-business/src/main/java/com/cec/business/domain/bo/ChangeCancelBo.java`

```java
@Data
public class ChangeCancelBo {
    @NotNull(message = "变更申请ID不能为空")
    private Long infoId;
    
    @NotBlank(message = "取消原因代码不能为空")
    @Size(max = 50, message = "取消原因代码长度不能超过50个字符")
    private String code;
    
    @Size(max = 500, message = "取消原因描述长度不能超过500个字符")
    private String reason;
}
```

### 2. 接口变更

**原接口**:
```
PUT /api/business/changeInfo/cancel/{infoId}
```

**新接口**:
```
PUT /api/business/changeInfo/cancel
Content-Type: application/json

{
  "infoId": 123,
  "code": "USER_REQUEST",
  "reason": "用户主动取消，不再需要此变更"
}
```

### 3. 修改的文件

1. **Controller**: `ChangeInfoController.java`
   - 修改 `cancelChange` 方法签名
   - 从 `@PathVariable Long infoId` 改为 `@RequestBody ChangeCancelBo bo`

2. **Service接口**: `IChangeInfoService.java`
   - 修改 `cancelChange` 方法签名

3. **Service实现**: `ChangeInfoServiceImpl.java`
   - 修改 `cancelChange` 方法实现
   - 在取消记录中包含取消原因代码和描述
   - 在SmartFlow取消流程时传递详细的取消原因

## 功能增强

### 取消原因记录

- **审批记录**: 待审批记录的 `opinion` 字段会记录格式化的取消原因
- **流程引擎**: SmartFlow取消流程时会传递详细的取消原因

### 格式化示例

取消原因会按以下格式记录：
```
流程已被创建人取消 [USER_REQUEST: 用户主动取消，不再需要此变更]
```

如果没有提供具体原因：
```
流程已被创建人取消 [BUSINESS_CHANGE: 无具体原因]
```

## 请求示例

### 完整请求
```json
{
  "infoId": 123,
  "code": "USER_REQUEST",
  "reason": "用户主动取消，不再需要此变更"
}
```

### 最小请求
```json
{
  "infoId": 123,
  "code": "BUSINESS_CHANGE"
}
```

## 验证规则

- `infoId`: 必填，变更申请ID
- `code`: 必填，最大50字符，取消原因代码
- `reason`: 可选，最大500字符，取消原因描述

## 向后兼容性

⚠️ **注意**: 此修改不向后兼容，前端需要相应更新调用方式。

## 测试

使用提供的 `test-cancel-change.http` 文件进行接口测试。
