package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.cec.business.domain.ChangeRecord;
import com.cec.business.domain.bo.ChangeCancelBo;
import com.cec.business.domain.bo.ChangeDelayBo;
import com.cec.business.domain.bo.ChangeInfoBo;
import com.cec.business.domain.bo.ChangeItemBo;
import com.cec.business.domain.bo.ChangeRecordBo;
import com.cec.business.domain.bo.ChangeRecordBo2;
import com.cec.business.domain.vo.ChangeDelayVo;
import com.cec.business.domain.vo.ChangeImplementingVo;
import com.cec.business.domain.vo.ChangeInfoVo;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.vo.ChangeRecordVo;
import com.cec.business.domain.vo.ChangeStatisticsVo;
import com.cec.business.domain.vo.FlowNodeVo;
import com.cec.business.service.IChangeDelayService;
import com.cec.business.service.IChangeInfoService;
import com.cec.business.service.IChangeItemService;
import com.cec.business.service.IChangeRecordService;
import com.cec.common.core.domain.R;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.core.validate.QueryGroup;
import com.cec.common.excel.utils.ExcelUtil;
import com.cec.common.idempotent.annotation.RepeatSubmit;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import com.cec.system.domain.vo.UserVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.function.BiFunction;
import java.util.function.Function;

/**
 * 变更申请
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/change")
@Slf4j
public class ChangeInfoController extends BaseController {

    private final IChangeInfoService changeInfoService;
    private final IChangeRecordService changeRecordService;
    private final IChangeItemService changeItemService;
    private final IChangeDelayService changeDelayService;

    /**
     * 查询变更申请列表
     */
    @SaCheckPermission("business:changeInfo:list")
    @GetMapping("/list")
    public TableDataInfo<ChangeItemVo> list(ChangeItemBo bo, PageQuery pageQuery) {
        return changeInfoService.queryPageList(bo, pageQuery);
    }

    /**
     * 导出变更申请列表
     */
    @SaCheckPermission("business:changeInfo:query")
    @Log(title = LogTitleEnum.CHANGE_MANAGEMENT, businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(ChangeItemBo bo, HttpServletResponse response) {
        try {
            // 创建一个足够大的分页参数，以获取所有数据
            PageQuery pageQuery = new PageQuery(5000, 1);

            // 获取变更申请列表数据
            TableDataInfo<ChangeItemVo> tableData = changeInfoService.queryPageList(bo, pageQuery);
            List<ChangeItemVo> list = tableData.getRows();

            // 获取国际化资源
            MessageSource messageSource = SpringUtil.getBean(MessageSource.class);
            Locale locale = LocaleContextHolder.getLocale();

            // 定义字段映射关系(字段名和国际化key的映射)
            Map<String, String> fieldMap = new HashMap<>();
            // 基本字段
            fieldMap.put("changeCode", "excel.header.changeCode");
            fieldMap.put("title", "excel.header.title");
            fieldMap.put("stage", "excel.header.stage");
            fieldMap.put("processorList", "excel.header.processorName");
            fieldMap.put("teamName", "excel.header.teamName");
            fieldMap.put("isUrgentChange", "excel.header.isUrgentChange");
            fieldMap.put("isNetworkFreezeChange", "excel.header.isNetworkFreezeChange");
            fieldMap.put("priority", "excel.header.priority");
            fieldMap.put("createTime", "excel.header.createTime");
            fieldMap.put("requester", "excel.header.requesterName");
            fieldMap.put("planTimeStart", "excel.header.planTimeStart");
            fieldMap.put("planTimeEnd", "excel.header.planTimeEnd");
            fieldMap.put("riskLevel", "excel.header.riskLevel");
            fieldMap.put("riskScore", "excel.header.riskScore");
            fieldMap.put("frozen", "excel.header.frozen");
            // 额外字段
            fieldMap.put("categoryNameList", "entity.categoryNameList");
            fieldMap.put("applicationName", "entity.applicationName");
            fieldMap.put("locationName", "excel.header.locationName");
            fieldMap.put("changeType", "excel.header.changeType");
            fieldMap.put("affectedUser", "excel.header.affectedUser");
            fieldMap.put("affectedApplicationName", "entity.affectedApplicationName");
            fieldMap.put("affectedDeviceName", "entity.affectedDeviceName");
            fieldMap.put("serviceDeliveryTeamList", "entity.serviceDeliveryTeamName");
            fieldMap.put("teamLeaderList", "entity.teamLeaderName");
            fieldMap.put("changeApproverList", "entity.changeApproverName");
            fieldMap.put("applicationOwnerList", "entity.applicationOwner");
            fieldMap.put("changeImplementerList", "entity.changeImplementerName");
            fieldMap.put("changeVerifierList", "entity.changeVerifierName");
            fieldMap.put("urgentChangeInspectorList", "entity.urgentChangeInspector");
            fieldMap.put("deptLeaderList", "entity.deptLeaderName");
            fieldMap.put("systemsAffectedNo", "excel.header.systemsAffectedNo");
            fieldMap.put("importantUsers", "excel.header.importantUsers");
            fieldMap.put("fallback", "excel.header.fallback");
            fieldMap.put("complexity", "excel.header.complexity");

            // 获取要导出的字段列表
            List<String> selectFieldNameList;
            // 如果指定了要导出的字段，则使用指定的字段
            if (CollUtil.isNotEmpty(bo.getSelectFiledNameList())) {
                selectFieldNameList = bo.getSelectFiledNameList();
            } else {
                selectFieldNameList = new ArrayList<>(fieldMap.keySet());
            }

            // 准备表头
            List<List<String>> headList = new ArrayList<>();
            for (String fieldName : selectFieldNameList) {
                String i18nKey = fieldMap.getOrDefault(fieldName, "excel.header." + fieldName);
                String headerName = messageSource.getMessage(i18nKey, null, fieldName, locale);
                headList.add(Collections.singletonList(headerName));
            }

            // 准备数据
            List<List<Object>> dataList = new ArrayList<>();
            for (ChangeItemVo item : list) {
                List<Object> rowData = new ArrayList<>();
                for (String fieldName : selectFieldNameList) {
                    rowData.add(getFieldValue(messageSource, locale, item, fieldName));
                }
                dataList.add(rowData);
            }

            // 准备响应
            String sheetName = messageSource.getMessage("excel.sheet.changeList", null, "变更列表", locale);
            String filename = ExcelUtil.encodingFilename(sheetName + "[Restricted]");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            com.cec.common.core.utils.file.FileUtils.setAttachmentResponseHeader(response, filename);

            // 使用EasyExcel API导出
            EasyExcelFactory.write(response.getOutputStream())
                .autoCloseStream(false)
                // 注册自定义列宽处理器
                .registerWriteHandler(new com.cec.common.excel.handler.CustomColumnWidthStrategy())
                // 开始写入
                .head(headList)
                .sheet(sheetName)
                .doWrite(dataList);
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new ServiceException("导出Excel异常");
        }
    }

    // 字段处理策略映射
    private static final Map<String, Function<ChangeItemVo, Object>> FIELD_HANDLERS = Map.of(
        "changeCode", ChangeItemVo::getChangeCode,
        "title", ChangeItemVo::getTitle,
        "teamName", ChangeItemVo::getTeamName,
        "createTime", ChangeItemVo::getCreateTime,
        "planTimeStart", ChangeItemVo::getPlanTimeStart,
        "planTimeEnd", ChangeItemVo::getPlanTimeEnd,
        "riskScore", ChangeItemVo::getRiskScore,
        "locationName", ChangeItemVo::getLocationName,
        "changeType", ChangeItemVo::getChangeType,
        "affectedUser", ChangeItemVo::getAffectedUser
    );

    // 国际化前缀常量
    private static final String ENUM_WHETHER_PREFIX = "enum.whether.";
    private static final String ENUM_CHANGE_STAGE_PREFIX = "enum.change.stage.";
    private static final String ENUM_LEVEL_PREFIX = "enum.level.";
    private static final String ENUM_RISK_LEVEL_PREFIX = "enum.risk.level.";
    private static final String ENUM_SYSTEMS_AFFECTED_PREFIX = "enum.systems.affected.";
    private static final String ENUM_IMPORTANT_USERS_PREFIX = "enum.important.users.";
    private static final String ENUM_FALLBACK_PREFIX = "enum.fallback.";
    private static final String ENUM_COMPLEXITY_PREFIX = "enum.complexity.";

    // 枚举字段处理策略
    private static final Map<String, BiFunction<ChangeItemVo, BiFunction<String, Enum<?>, String>, Object>> ENUM_HANDLERS = Map.of(
        "stage", (item, formatter) -> formatter.apply(ENUM_CHANGE_STAGE_PREFIX, item.getStage()),
        "isUrgentChange", (item, formatter) -> formatter.apply(ENUM_WHETHER_PREFIX, item.getIsUrgentChange()),
        "isNetworkFreezeChange", (item, formatter) -> formatter.apply(ENUM_WHETHER_PREFIX, item.getIsNetworkFreezeChange()),
        "frozen", (item, formatter) -> formatter.apply(ENUM_WHETHER_PREFIX, item.getIsNetworkFreezeChange()),
        "priority", (item, formatter) -> formatter.apply(ENUM_LEVEL_PREFIX, item.getPriority()),
        "riskLevel", (item, formatter) -> formatter.apply(ENUM_RISK_LEVEL_PREFIX, item.getRiskLevel()),
        "systemsAffectedNo", (item, formatter) -> formatter.apply(ENUM_SYSTEMS_AFFECTED_PREFIX, item.getSystemsAffectedNo()),
        "importantUsers", (item, formatter) -> formatter.apply(ENUM_IMPORTANT_USERS_PREFIX, item.getImportantUsers()),
        "fallback", (item, formatter) -> formatter.apply(ENUM_FALLBACK_PREFIX, item.getFallback()),
        "complexity", (item, formatter) -> formatter.apply(ENUM_COMPLEXITY_PREFIX, item.getComplexity())
    );

    // 用户列表字段处理策略
    private static final Map<String, Function<ChangeItemVo, List<UserVo>>> USER_LIST_HANDLERS = Map.of(
        "processorList", ChangeItemVo::getProcessorList,
        "serviceDeliveryTeamList", ChangeItemVo::getServiceDeliveryTeamList,
        "teamLeaderList", ChangeItemVo::getTeamLeaderList,
        "changeApproverList", ChangeItemVo::getChangeApproverList,
        "applicationOwnerList", ChangeItemVo::getApplicationOwnerList,
        "changeImplementerList", ChangeItemVo::getChangeImplementerList,
        "changeVerifierList", ChangeItemVo::getChangeVerifierList,
        "urgentChangeInspectorList", ChangeItemVo::getUrgentChangeInspectorList,
        "deptLeaderList", ChangeItemVo::getDeptLeaderList
    );

    // 字符串列表字段处理策略
    private static final Map<String, Function<ChangeItemVo, List<String>>> STRING_LIST_HANDLERS = Map.of(
        "categoryNameList", ChangeItemVo::getCategoryNameList,
        "applicationName", ChangeItemVo::getApplicationName,
        "affectedApplicationName", ChangeItemVo::getAffectedApplicationName,
        "affectedDeviceName", ChangeItemVo::getAffectedDeviceName
    );

    /**
     * 根据字段名获取对应字段值，并处理国际化
     *
     * @param messageSource 国际化资源
     * @param locale        当前语言环境
     * @param item          变更项数据
     * @param fieldName     字段名
     * @return 格式化后的字段值
     */
    private Object getFieldValue(MessageSource messageSource, Locale locale, ChangeItemVo item, String fieldName) {
        // 基本字段处理
        if (FIELD_HANDLERS.containsKey(fieldName)) {
            return FIELD_HANDLERS.get(fieldName).apply(item);
        }

        // 枚举字段处理
        if (ENUM_HANDLERS.containsKey(fieldName)) {
            BiFunction<String, Enum<?>, String> formatter = (prefix, enumValue) ->
                formatEnumValue(messageSource, locale, prefix, enumValue);
            return ENUM_HANDLERS.get(fieldName).apply(item, formatter);
        }

        // 用户列表字段处理
        if (USER_LIST_HANDLERS.containsKey(fieldName)) {
            List<UserVo> users = USER_LIST_HANDLERS.get(fieldName).apply(item);
            if (users == null || users.isEmpty()) {
                return "";
            }
            return formatStringList(users.stream().map(UserVo::getStaffName).toList());
        }

        // 字符串列表字段处理
        if (STRING_LIST_HANDLERS.containsKey(fieldName)) {
            List<String> stringList = STRING_LIST_HANDLERS.get(fieldName).apply(item);
            if (stringList == null || stringList.isEmpty()) {
                return "";
            }
            return formatStringList(stringList);
        }

        // 特殊字段处理
        if ("requester".equals(fieldName)) {
            return item.getRequester() != null ? item.getRequester().getStaffName() : "";
        }

        return "";
    }

    /**
     * 格式化枚举值，处理国际化
     *
     * @param messageSource 国际化资源
     * @param locale        当前语言环境
     * @param keyPrefix     国际化key前缀
     * @param enumValue     枚举值
     * @return 国际化后的字符串
     */
    private String formatEnumValue(MessageSource messageSource, Locale locale, String keyPrefix, Enum<?> enumValue) {
        if (enumValue == null) {
            return "";
        }
        String key = keyPrefix + enumValue.name();
        return messageSource.getMessage(key, null, enumValue.toString(), locale);
    }

    /**
     * 格式化字符串列表为逗号分隔的字符串
     *
     * @param list 字符串列表
     * @return 格式化后的字符串
     */
    private String formatStringList(List<String> list) {
        return list != null ? String.join(", ", list) : "";
    }

    /**
     * 获取变更申请详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:changeInfo:detail")
    @GetMapping("/{id}")
    public R<ChangeInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                   @PathVariable Long id) {
        return R.ok(changeInfoService.queryById(id));
    }

    /**
     * 新增变更申请
     */
    @SaCheckPermission("business:changeInfo:edit")
    @Log(title = LogTitleEnum.CHANGE_MANAGEMENT, businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody ChangeInfoBo bo) {
        return R.ok(changeInfoService.insertByBo(bo));
    }

    /**
     * 修改变更申请
     */
    @SaCheckPermission("business:changeInfo:edit")
    @Log(title = LogTitleEnum.CHANGE_MANAGEMENT, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ChangeInfoBo bo) {
        return toAjax(changeInfoService.updateByBo(bo));
    }

    /**
     * 删除变更申请
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:changeInfo:remove")
    @Log(title = LogTitleEnum.CHANGE_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(changeInfoService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 根据infoId删除变更申请（仅允许删除草稿状态且为当前用户创建）
     * 同时删除changeInfo和changeItem表中的相关数据
     *
     * @param infoId 变更申请ID
     */
    @Log(title = LogTitleEnum.CHANGE_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/draft/{infoId}")
    public R<Void> removeDraft(@NotNull(message = "变更申请ID不能为空")
                               @PathVariable Long infoId) {
        return toAjax(changeInfoService.deleteDraftByInfoId(infoId));
    }

    /**
     * 审批
     */
    @Log(title = LogTitleEnum.CHANGE_APPROVAL, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/record")
    @SaCheckPermission("business:changeRecord:update")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ChangeRecordBo2 bo) {
        changeInfoService.updateByBo(bo);
        return R.ok();
    }

    /**
     * 查询变更待审批列表（个人）
     */
    @GetMapping("/record/list")
    @SaCheckPermission("business:changeRecord:list")
    public TableDataInfo<ChangeRecordVo> list(ChangeRecordBo bo, PageQuery pageQuery) {
        return changeRecordService.queryPageList(bo, pageQuery);
    }

    /**
     * 取消变更流程
     *
     * @param bo 取消变更请求参数
     */
    @Log(title = LogTitleEnum.CHANGE_CANCEL, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/cancel")
    @SaCheckPermission("business:changeInfo:edit")
    public R<Void> cancelChange(@Validated @RequestBody ChangeCancelBo bo) {
        return toAjax(changeInfoService.cancelChange(bo));
    }

    /**
     * 获取变更实施与核查
     */
    @GetMapping("/implementing/{infoId}")
    @SaCheckPermission("business:changeInfo:detail")
    public R<ChangeImplementingVo> implementing(@NotNull(message = "变更申请ID不能为空") @PathVariable Long infoId) {
        return R.ok(changeRecordService.getImplementing(infoId));
    }

    /**
     * 获取审批明细表
     *
     * @param infoId 变更申请ID
     * @return 审批记录明细列表
     */
    @GetMapping("/record/detail/{infoId}")
    @SaCheckPermission("business:changeInfo:detail")
    public R<List<ChangeImplementingVo.ChangeRecordVo>> listRecord(@NotNull(message = "变更申请ID不能为空") @PathVariable Long infoId) {
        return R.ok(changeRecordService.listRecord(infoId));
    }

    /**
     * 获取审批明细
     *
     * @param recordId 审批申请ID
     * @return 审批记录明细列表
     */
    @GetMapping("/record/{recordId}")
    @SaCheckPermission("business:changeInfo:detail")
    public R<ChangeRecord> getRecord(@NotNull(message = "审批ID不能为空") @PathVariable Long recordId) {
        return R.ok(changeRecordService.getRecord(recordId));
    }

    /**
     * 获取流程预览节点
     */
    @GetMapping("/flow/preview/{infoId}")
    @SaCheckPermission("business:changeInfo:detail")
    public R<FlowNodeVo> flowPreview(@NotNull(message = "变更申请ID不能为空") @PathVariable Long infoId) {
        return R.ok(changeRecordService.getFlowPreview(infoId));
    }

    /**
     * 获取变更统计
     *
     * @param dateStart 查询日期起始 yyyyMM
     * @param dateEnd   查询日期起始 yyyyMM
     * @param teamId    团队ID
     * @param stageList  【多选】2-计划中、3-待审批、4-已审批、5-实施中、6-已完成、7-已拒绝、8-已回滚、9-已取消、10-待验证
     * @param pageQuery 分页参数
     * @return 变更统计
     */
    @GetMapping("/statistics")
    @SaCheckPermission("business:changeStatistics:list")
    public R<ChangeStatisticsVo> statistics(@NotNull(message = "起始日期不能为空") @RequestParam String dateStart,
                                            @NotNull(message = "结束日期不能为空") @RequestParam String dateEnd,
                                            @RequestParam(required = false) Long teamId,
                                            @RequestParam(required = false) List<Integer> stageList,
                                            PageQuery pageQuery) {
        return R.ok(changeItemService.getStatistics(dateStart, dateEnd, teamId, stageList, pageQuery));
    }

    /**
     * 查询变更延期记录列表
     */
    // @SaCheckPermission("business:changeDelay:list")
    @SaCheckPermission("business:changeInfo:detail")
    @GetMapping("/delay/list")
    public List<ChangeDelayVo> list(@Validated(QueryGroup.class) ChangeDelayBo bo) {
        return changeDelayService.queryPageList(bo);
    }

    /**
     * 新增变更延期记录
     */
//    @SaCheckPermission("business:changeDelay:add")
    @Log(title = LogTitleEnum.CHANGE_DELAY, businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping("/delay")
    public R<Void> add(@Validated(AddGroup.class) @RequestBody ChangeDelayBo bo) {
        return toAjax(changeDelayService.insertByBo(bo));
    }

    /**
     * 修改变更延期记录
     */
//    @SaCheckPermission("business:changeDelay:edit")
    @Log(title = LogTitleEnum.CHANGE_DELAY, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/delay")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody ChangeDelayBo bo) {
        return toAjax(changeDelayService.updateByBo(bo));
    }

    /**
     * 导出变更统计数据
     *
     * @param dateStart 查询日期起始 yyyyMM
     * @param dateEnd   查询日期起始 yyyyMM
     * @param teamId    团队ID
     * @param stageList    【多选】2-计划中、3-待审批、4-已审批、5-实施中、6-已完成、7-已拒绝、8-已回滚、9-已取消、10-待验证
     */
    @SaCheckPermission("business:changeStatistics:export")
    @Log(title = LogTitleEnum.STATISTICS, businessType = BusinessType.EXPORT)
    @GetMapping("/statistics/export")
    public void exportStatistics(@NotNull(message = "dateStart不能为空") @RequestParam String dateStart,
                                 @NotNull(message = "dateEnd不能为空") @RequestParam String dateEnd,
                                 @RequestParam(required = false) Long teamId,
                                 @RequestParam(required = false) List<Integer> stageList,
                                 HttpServletResponse response) {
        try {
            // 获取变更统计数据
            ChangeStatisticsVo statistics = changeItemService.getStatistics(dateStart, dateEnd, teamId, stageList, null);
            List<ChangeStatisticsVo.ChangeItemVo> rows = statistics.getData().getRows();

            // 确保formattedProcessTime都已经设置
            rows.forEach(item -> {
                if (item.getFormattedProcessTime() == null) {
                    item.getFormattedProcessTime();
                }
            });

            // 准备响应
            String sheetName = "变更统计";
            String filename = ExcelUtil.encodingFilename(sheetName + "[Restricted]");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            com.cec.common.core.utils.file.FileUtils.setAttachmentResponseHeader(response, filename);

            // 获取国际化资源
            MessageSource messageSource = SpringUtil.getBean(MessageSource.class);
            Locale locale = LocaleContextHolder.getLocale();

            // 准备国际化表头
            List<List<String>> headList = new ArrayList<>();
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.changeCode", null, "Change Code", locale)));
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.title", null, "Title", locale)));
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.stage", null, "Stage", locale)));
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.teamName", null, "Team Name", locale)));
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.isUrgentChange", null, "Is Urgent Change", locale)));
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.priority", null, "Priority", locale)));
            headList.add(Collections.singletonList(messageSource.getMessage("excel.header.processTime", null, "Process Time", locale)));

            // 准备数据列表
            List<List<Object>> dataList = new ArrayList<>();
            for (ChangeStatisticsVo.ChangeItemVo item : rows) {
                List<Object> rowData = new ArrayList<>();
                rowData.add(item.getChangeCode());
                rowData.add(item.getTitle());

                // 处理枚举值国际化
                String stageKey = ENUM_CHANGE_STAGE_PREFIX + item.getStage().name();
                String stage = messageSource.getMessage(stageKey, null, item.getStage().getInfo(), locale);
                rowData.add(stage);

                rowData.add(item.getTeamName());

                // 处理是否枚举值国际化
                String urgentKey = ENUM_WHETHER_PREFIX + item.getIsUrgentChange().name();
                String urgent = messageSource.getMessage(urgentKey, null, item.getIsUrgentChange().getInfo(), locale);
                rowData.add(urgent);

                // 处理优先级枚举值国际化
                String priorityKey = ENUM_LEVEL_PREFIX + item.getPriority().name();
                String priority = messageSource.getMessage(priorityKey, null, item.getPriority().getInfo(), locale);
                rowData.add(priority);

                rowData.add(item.getFormattedProcessTime());

                dataList.add(rowData);
            }

            // 直接使用EasyExcel API
            EasyExcelFactory.write(response.getOutputStream())
                .autoCloseStream(false)
                // 注册自定义列宽处理器
                .registerWriteHandler(new com.cec.common.excel.handler.CustomColumnWidthStrategy())
                // 开始写入
                .head(headList)
                .sheet(sheetName)
                .doWrite(dataList);
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new ServiceException("导出Excel异常");
        }
    }
}
