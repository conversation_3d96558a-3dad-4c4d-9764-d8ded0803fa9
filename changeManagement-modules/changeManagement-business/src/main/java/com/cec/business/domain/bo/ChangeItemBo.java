package com.cec.business.domain.bo;

import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.common.core.validate.EditGroup;
import io.github.linpeilie.annotations.AutoMapper;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * 变更申请记录业务对象 cm_change_item
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@AutoMapper(target = ChangeItem.class, reverseConvertGenerate = false)
public class ChangeItemBo {

    /**
     * 字段展示
     * changeCode/title/stage/processorList/teamName/isUrgentChange/priority/createTime
     * <p>
     * isNetworkFreezeChange/applicationName/locationName/changeType/requesterName/planTimeStart/planTimeEnd/affectedUser/affectedApplicationName/affectedDeviceName/teamLeaderList/changeApproverList/applicationOwnerList/changeImplementerList/changeVerifierList/urgentChangeInspectorList/deptLeaderList/systemsAffectedNo/importantUsers/fallback/complexity/riskLevel/riskScore/serviceDeliveryTeamList
     */
    private List<String> selectFiledNameList;

    /**
     *
     */
    @NotNull(message = "不能为空", groups = {EditGroup.class})
    private Long id;

    /**
     * 变更ID
     */
    private Long changeId;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 变更标题
     */
    private String title;

    /**
     * 阶段<多选>
     * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
     */
    private List<ChangeStageEnum> stageList;

    /**
     * 当前处理人id
     */
    private String processorId;

    /**
     * 当前处理人名字
     */
    private String processorName;

    /**
     * 变更组ID<多选>
     */
    private List<Long> teamIdList;

    /**
     * 变更组名
     */
    private String teamName;

    /**
     * 是否紧急变更<多选>
     * 1-是 2-否
     */
    private List<WhetherEnum> isUrgentChangeList;

    /**
     * 优先级<多选>
     * 1-高 2-中 3-低
     */
    private List<LevelEnum> priorityList;

    /**
     * 是否封网变更<多选>
     * （1-是 2-否）
     */
    private List<WhetherEnum> isNetworkFreezeChangeList;

    /**
     * 地点名
     */
    private String locationName;

    /**
     * 申请人名称
     */
    private String requesterName;

    /**
     * 地点id<多选>
     */
    private List<Long> locationIdList;

    /**
     * 创建时间起（yyyy-MM-dd HH:mm:ss）
     */
    private String createTimeStart;

    /**
     * 创建时间止（yyyy-MM-dd HH:mm:ss）
     */
    private String createTimeEnd;

    private String code;
}
