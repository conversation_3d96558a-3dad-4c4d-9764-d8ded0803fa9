package com.cec.business.domain.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeItem;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.system.domain.vo.UserVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;
import java.util.List;


/**
 * 封网申请记录视图对象 cm_network_freeze_item
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@ExcelIgnoreUnannotated
@AutoMapper(target = NetworkFreezeItem.class)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NetworkFreezeItemVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @ExcelProperty(value = "")
    private Long id;

    /**
     * 变更ID
     */
    @ExcelProperty(value = "变更ID")
    private Long freezeId;

    /**
     * 变更编号
     */
    @ExcelProperty(value = "变更编号")
    private String freezeCode;

    /**
     * 封网名称
     */
    @ExcelProperty(value = "封网名称")
    private String title;

    /**
     * 封网等级
     */
    @ExcelProperty(value = "封网等级")
    private NetworkFreezeLevelEnum level;

    /**
     * 阶段
     */
    @ExcelProperty(value = "阶段")
    private NetworkFreezeStageEnum stage;

    /**
     * 当前处理人List
     */
    private List<UserVo> processorList;

    /**
     * 封禁应用
     */
    private String freezeApps;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 封网地区和时间;存json数组
     */
    private List<NetworkFreezeArea> periodAndArea;

    /**
     * 流程实例id
     */
    private String instanceId;

    /**
     * 申请人
     */
    private UserVo requester;

    /**
     * 发布时间
     */
    private Date publishTime;
}
