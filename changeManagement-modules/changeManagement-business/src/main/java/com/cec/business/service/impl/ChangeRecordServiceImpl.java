package com.cec.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.ChangeInfo;
import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.ChangeRecord;
import com.cec.business.domain.bo.ChangeRecordBo;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.ChangeImplementingVo;
import com.cec.business.domain.vo.ChangeRecordVo;
import com.cec.business.domain.vo.FlowNodeVo;
import com.cec.business.domain.vo.LocationVo;
import com.cec.business.extra.SmartFlowUtils;
import com.cec.business.extra.resp.FlowInstanceResp;
import com.cec.business.mapper.ChangeInfoMapper;
import com.cec.business.mapper.ChangeItemMapper;
import com.cec.business.mapper.ChangeRecordMapper;
import com.cec.business.service.IChangeRecordService;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 变更申请记录流水Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ChangeRecordServiceImpl implements IChangeRecordService {

    private final ChangeRecordMapper baseMapper;
    private final ChangeInfoMapper changeInfoMapper;
    private final ChangeItemMapper changeItemMapper;
    private final ChangeRecordMapper changeRecordMapper;
    private final SmartFlowUtils smartFlowUtils;

    /**
     * 查询变更申请记录流水
     *
     * @param id 主键
     * @return 变更申请记录流水
     */
    @Override
    public ChangeRecordVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询变更申请记录流水列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请记录流水分页列表
     */
    @Override
    public TableDataInfo<ChangeRecordVo> queryPageList(ChangeRecordBo bo, PageQuery pageQuery) {
        // 获取当前登录用户
        LoginUser user = LoginHelper.getLoginUser();

        // 确定要返回的字段
        List<String> code = bo.getSelectFiledNameList() != null && !bo.getSelectFiledNameList().isEmpty()
            ? bo.getSelectFiledNameList()
            : List.of("changeCode", "title", "processorStatus", "processorList", "teamName", "isUrgentChange", "priority", "processorTime", "nodeName");

        // 构建查询条件
        LambdaQueryWrapper<ChangeRecord> lqw = buildQueryWrapper(bo);

        // 只查询当前登录用户的处理记录
        lqw.apply("JSON_EXTRACT(processor, '$.userId') = {0} ", user.getUserId());

        // 获取排序字段和排序方式
        String orderByColumn = pageQuery.getOrderByColumn();
        String isAsc = pageQuery.getIsAsc();

        // 清空PageQuery中的排序设置，防止它自动添加排序条件
        pageQuery.setOrderByColumn(null);
        pageQuery.setIsAsc(null);

        // 构建自定义排序SQL
        StringBuilder orderBySql = new StringBuilder("ORDER BY processor_status=4 DESC");
        if (StringUtils.isNotBlank(orderByColumn)) {
            String columnName = com.cec.common.core.utils.StringUtils.toUnderScoreCase(orderByColumn);
            String direction = "asc".equalsIgnoreCase(isAsc) ? "ASC" : "DESC";
            orderBySql.append(", ").append(columnName).append(" ").append(direction);
        }
        orderBySql.append(", id DESC");

        // 设置排序SQL
        lqw.last(orderBySql.toString());
        // 执行查询
        Page<ChangeRecordVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        List<ChangeRecordVo> records = result.getRecords();

        if (records != null && !records.isEmpty()) {
            // 判断是否需要查询额外字段
            boolean needExtraFields = code.stream().anyMatch(field ->
                List.of("isNetworkFreezeChange", "applicationName", "locationName", "changeType",
                    "requester", "planTimeStart", "planTimeEnd", "affectedUser",
                    "affectedApplicationName", "affectedDeviceName", "teamLeaderList",
                    "changeApproverList", "applicationOwnerList", "changeImplementerList",
                    "changeVerifierList", "urgentChangeInspectorList", "deptLeaderList",
                    "systemsAffectedNo", "importantUsers", "fallback", "complexity",
                    "riskLevel", "riskScore", "serviceDeliveryTeamList", "categoryNameList").contains(field));

            Map<Long, ChangeInfo> infoMap = needExtraFields ? changeInfoMapper
                .selectByIds(
                    records.stream().map(ChangeRecordVo::getChangeId).toList())
                .stream()
                .collect(java.util.stream.Collectors.toMap(ChangeInfo::getId, info -> info, (k1, k2) -> k1)) : Collections.emptyMap();

            // 过滤记录中的字段
            result.setRecords(records.stream()
                .map(item -> {
                    // 创建新对象
                    ChangeRecordVo filteredItem = new ChangeRecordVo();

                    // 保留基本ID
                    filteredItem.setId(item.getId());
                    filteredItem.setChangeItemId(item.getChangeItemId());
                    filteredItem.setChangeId(item.getChangeId());
                    filteredItem.setIsSealAddition(item.getIsSealAddition());

                    // 设置默认字段
                    if (code.contains("changeCode")) filteredItem.setChangeCode(item.getChangeCode());
                    if (code.contains("title")) filteredItem.setTitle(item.getTitle());
                    if (code.contains("processorStatus")) filteredItem.setProcessorStatus(item.getProcessorStatus());
                    if (code.contains("processorList")) filteredItem.setProcessor(item.getProcessor());
                    if (code.contains("teamName")) filteredItem.setTeamName(item.getTeamName());
                    if (code.contains("isUrgentChange")) filteredItem.setIsUrgentChange(item.getIsUrgentChange());
                    if (code.contains("priority")) filteredItem.setPriority(item.getPriority());
                    if (code.contains("processorTime")) filteredItem.setProcessorTime(item.getProcessorTime());
                    if (code.contains("stage")) filteredItem.setStage(item.getStage());
                    if (code.contains("nodeName")) filteredItem.setNodeName(item.getNodeName());

                    // 处理需要额外查询的字段
                    if (needExtraFields) {
                        // 可以通过关联查询或其他方式获取额外字段值
                        // 这里先尝试从已有字段填充可能的对应关系
                        // if (code.contains("processorId")) filteredItem.setProcessorId(item.getProcessorId());
                        if (code.contains("teamId")) filteredItem.setTeamId(item.getTeamId());
                        if (code.contains("opinion")) filteredItem.setOpinion(item.getOpinion());
                        if (code.contains("isSealAddition")) filteredItem.setIsSealAddition(item.getIsSealAddition());

                        // 支持ChangeInfoVo中的额外字段，需要从关联表获取数据
                        ChangeInfo info = infoMap.get(item.getChangeId());

                        if (code.contains("isNetworkFreezeChange"))
                            filteredItem.setIsNetworkFreezeChange(info.getIsNetworkFreezeChange());
                        if (code.contains("applicationName"))
                            filteredItem.setApplicationName(info.getApplicationName());
                        if (code.contains("locationName")) {
                            String locationNames = Optional.ofNullable(info.getLocationList())
                                .filter(CollUtil::isNotEmpty)
                                .map(list -> list.stream()
                                    .map(LocationVo::getLocationName)
                                    .filter(StringUtils::isNotBlank)
                                    .collect(Collectors.joining(",")))
                                .orElse("");
                            filteredItem.setLocationName(locationNames);
                        }
                        if (code.contains("changeType")) filteredItem.setChangeType(info.getChangeType());
                        if (code.contains("requester")) filteredItem.setRequester(info.getRequester());
                        if (code.contains("planTimeStart")) filteredItem.setPlanTimeStart(info.getPlanTimeStart());
                        if (code.contains("planTimeEnd")) filteredItem.setPlanTimeEnd(info.getPlanTimeEnd());
                        if (code.contains("affectedUser")) filteredItem.setAffectedUser(info.getAffectedUser());
                        if (code.contains("affectedApplicationName"))
                            filteredItem.setAffectedApplicationName(info.getAffectedApplicationName());
                        if (code.contains("affectedDeviceName"))
                            filteredItem.setAffectedDeviceName(info.getAffectedDeviceName());
                        if (code.contains("teamLeaderList")) filteredItem.setTeamLeaderList(info.getTeamLeaderList());
                        if (code.contains("changeApproverList"))
                            filteredItem.setChangeApproverList(info.getChangeApproverList());
                        if (code.contains("applicationOwnerList"))
                            filteredItem.setApplicationOwnerList(info.getApplicationOwnerList());
                        if (code.contains("changeImplementerList"))
                            filteredItem.setChangeImplementerList(info.getChangeImplementerList());
                        if (code.contains("changeVerifierList"))
                            filteredItem.setChangeVerifierList(info.getChangeVerifierList());
                        if (code.contains("urgentChangeInspectorList"))
                            filteredItem.setUrgentChangeInspectorList(info.getUrgentChangeInspectorList());
                        if (code.contains("deptLeaderList")) filteredItem.setDeptLeaderList(info.getDeptLeaderList());
                        if (code.contains("systemsAffectedNo"))
                            filteredItem.setSystemsAffectedNo(info.getSystemsAffectedNo());
                        if (code.contains("importantUsers"))
                            filteredItem.setImportantUsers(info.getImportantUsers());
                        if (code.contains("fallback")) filteredItem.setFallback(info.getFallback());
                        if (code.contains("complexity")) filteredItem.setComplexity(info.getComplexity());
                        if (code.contains("riskLevel")) filteredItem.setRiskLevel(info.getRiskLevel());
                        if (code.contains("riskScore")) filteredItem.setRiskScore(info.getRiskScore().intValue());
                        if (code.contains("categoryNameList"))
                            filteredItem.setCategoryNameList(info.getCategoryNameList());
                        if (code.contains("serviceDeliveryTeamList"))
                            filteredItem.setServiceDeliveryTeamList(info.getServiceDeliveryTeamList());

                    }

                    return filteredItem;
                }).toList());
        }

        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的变更申请记录流水列表
     *
     * @param bo 查询条件
     * @return 变更申请记录流水列表
     */
    @Override
    public List<ChangeRecordVo> queryList(ChangeRecordBo bo) {
        LambdaQueryWrapper<ChangeRecord> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ChangeRecord> buildQueryWrapper(ChangeRecordBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<ChangeRecord> lqw = Wrappers.lambdaQuery();
        // lqw.last("ORDER BY processor_status=4 DESC, id DESC");
        lqw.eq(bo.getChangeItemId() != null, ChangeRecord::getChangeItemId, bo.getChangeItemId());
        lqw.eq(bo.getChangeId() != null, ChangeRecord::getChangeId, bo.getChangeId());
        lqw.like(StringUtils.isNotBlank(bo.getChangeCode()), ChangeRecord::getChangeCode, bo.getChangeCode());
        lqw.like(StringUtils.isNotBlank(bo.getTitle()), ChangeRecord::getTitle, bo.getTitle());
        lqw.in(CollUtil.isNotEmpty(bo.getTeamIdList()), ChangeRecord::getTeamId, bo.getTeamIdList());
        lqw.apply(StringUtils.isNotBlank(bo.getProcessorName()), "JSON_EXTRACT(processor, '$.staffName') LIKE CONCAT('%', {0}, '%')", bo.getProcessorName());
        lqw.in(CollUtil.isNotEmpty(bo.getStageList()), ChangeRecord::getStage, bo.getStageList());
        // 是否紧急变更
        lqw.in(CollUtil.isNotEmpty(bo.getIsUrgentChangeList()), ChangeRecord::getIsUrgentChange, bo.getIsUrgentChangeList());
        lqw.in(CollUtil.isNotEmpty(bo.getIsNetworkFreezeChangeList()), ChangeRecord::getIsNetworkFreezeChange, bo.getIsNetworkFreezeChangeList());
        lqw.apply(StringUtils.isNotBlank(bo.getRequesterName()), "JSON_EXTRACT(requester, '$.staffName') LIKE CONCAT('%', {0}, '%')", bo.getRequesterName());
        // 优先级
        lqw.in(CollUtil.isNotEmpty(bo.getPriorityList()), ChangeRecord::getPriority, bo.getPriorityList());
        // 修改为匹配 locationList 中的 locationId 字段，支持多个locationId查询
        if (CollUtil.isNotEmpty(bo.getLocationIdList())) {
            lqw.and(wrapper -> {
                bo.getLocationIdList().forEach(locationId ->
                    wrapper.or().apply("JSON_SEARCH(location_list, 'one', {0}, NULL, '$[*].locationId') IS NOT NULL", locationId)
                );
            });
        }
        // 节点名
        lqw.in(CollUtil.isNotEmpty(bo.getNodeNameList()), ChangeRecord::getNodeName, bo.getNodeNameList());
        // 审批状态
        lqw.eq(bo.getProcessorStatus() != null, ChangeRecord::getProcessorStatus, bo.getProcessorStatus());
        lqw.gt(StringUtils.isNotBlank(bo.getProcessorTimeStart()), ChangeRecord::getProcessorTime, bo.getProcessorTimeStart());
        lqw.lt(StringUtils.isNotBlank(bo.getProcessorTimeEnd()), ChangeRecord::getProcessorTime, bo.getProcessorTimeEnd());
        lqw.and(wrapper -> wrapper
            .isNull(ChangeRecord::getIsOrSignProcessed)
            .or()
            .ne(ChangeRecord::getIsOrSignProcessed, WhetherEnum.YES));

        return lqw;
    }

    /**
     * 新增变更申请记录流水
     *
     * @param bo 变更申请记录流水
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ChangeRecordBo bo) {
        ChangeRecord add = MapstructUtils.convert(bo, ChangeRecord.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改变更申请记录流水
     *
     * @param bo 变更申请记录流水
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ChangeRecordBo bo) {
        ChangeRecord update = MapstructUtils.convert(bo, ChangeRecord.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ChangeRecord entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除变更申请记录流水信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            // TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 获取变更进行中的列表
     *
     * @param infoId 变更申请ID
     * @return 变更实施进度信息
     */
    @Override
    public ChangeImplementingVo getImplementing(Long infoId) {
        // 创建结果对象
        ChangeImplementingVo result = new ChangeImplementingVo();

        // 1. 查询实施中和待验证阶段的、不在待审批状态的记录
        LambdaQueryWrapper<ChangeRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ChangeRecord::getChangeId, infoId); // 根据变更ID过滤
        queryWrapper.and(wrapper -> wrapper
            .eq(ChangeRecord::getStage, ChangeStageEnum.IMPLEMENTING)
            .or()
            .eq(ChangeRecord::getStage, ChangeStageEnum.PENDING_VERIFICATION));
        queryWrapper.ne(ChangeRecord::getProcessorStatus, ProcessorStatusEnum.PENDING); // 排除待审批状态

        // 2. 按审批时间及ID升序排序
        queryWrapper.orderByAsc(ChangeRecord::getProcessorTime);
        queryWrapper.orderByAsc(ChangeRecord::getId);

        // 查询结果
        List<ChangeRecord> records = baseMapper.selectList(queryWrapper);

        // 3. 转换为VO
        List<ChangeImplementingVo.ChangeRecordVo> recordVoList = new ArrayList<>();

        if (records != null && !records.isEmpty()) {
            recordVoList = records.stream().map(record -> {
                ChangeImplementingVo.ChangeRecordVo recordVo = new ChangeImplementingVo.ChangeRecordVo();
                recordVo.setRecordId(record.getId());
                recordVo.setChangeItemId(record.getChangeItemId());
                recordVo.setChangeId(record.getChangeId());
                recordVo.setChangeCode(record.getChangeCode());
                recordVo.setProcessorStatus(record.getProcessorStatus());
                recordVo.setProcessor(record.getProcessor());
                recordVo.setProcessorTime(record.getProcessorTime());
                recordVo.setStep(record.getStep());
                recordVo.setOpinion(record.getOpinion());
                recordVo.setProcessOrder(record.getProcessOrder());
                return recordVo;
            }).collect(Collectors.toList());

            // 4. 设置结果字段
            result.setChangeRecordVoList(recordVoList);

            // 5. 处理result字段：
            // 获取实施中阶段记录
            List<ChangeRecord> implementingRecords = records.stream()
                .filter(record -> ChangeStageEnum.IMPLEMENTING.equals(record.getStage()))
                .collect(Collectors.toList());

            if (!implementingRecords.isEmpty()) {
                // 找出最大的processOrder值
                Integer maxOrder = implementingRecords.stream()
                    .map(ChangeRecord::getProcessOrder)
                    .filter(Objects::nonNull)
                    .max(Integer::compareTo)
                    .orElse(0);

                // 获取具有最大processOrder值的记录集合
                List<ChangeRecord> maxOrderRecords = implementingRecords.stream()
                    .filter(record -> maxOrder.equals(record.getProcessOrder()))
                    .collect(Collectors.toList());

                // 如果最大order值的记录集中存在已回滚，那么result为已回滚，否则为已变更
                boolean hasMaxOrderRolledBack = maxOrderRecords.stream()
                    .anyMatch(record -> ProcessorStatusEnum.ROLLED_BACK.equals(record.getProcessorStatus()));

                if (hasMaxOrderRolledBack) {
                    result.setResult(ProcessorStatusEnum.ROLLED_BACK);
                } else {
                    // 检查是否所有记录都是已实施状态
                    boolean allImplemented = maxOrderRecords.stream()
                        .allMatch(record -> ProcessorStatusEnum.IMPLEMENTED.equals(record.getProcessorStatus()));

                    if (allImplemented) {
                        result.setResult(ProcessorStatusEnum.IMPLEMENTED);
                    } else {
                        result.setResult(null);
                    }
                }
            } else {
                result.setResult(null);
            }
        }

        return result;
    }

    /**
     * 获取审批明细表
     *
     * @param infoId 变更申请ID
     * @return 审批记录列表
     */
    @Override
    public List<ChangeImplementingVo.ChangeRecordVo> listRecord(Long infoId) {
        // 1. 查询该变更申请的所有记录
        LambdaQueryWrapper<ChangeRecord> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ChangeRecord::getChangeId, infoId);
        queryWrapper.and(wrapper -> wrapper
            .isNull(ChangeRecord::getIsOrSignProcessed)
            .or()
            .ne(ChangeRecord::getIsOrSignProcessed, WhetherEnum.YES));

        queryWrapper.orderByAsc(ChangeRecord::getId);

        // 查询结果
        List<ChangeRecord> records = baseMapper.selectList(queryWrapper);

        // 3. 转换为VO
        List<ChangeImplementingVo.ChangeRecordVo> recordVoList = new ArrayList<>();

        if (records != null && !records.isEmpty()) {
            recordVoList = records.stream().map(record -> {
                ChangeImplementingVo.ChangeRecordVo recordVo = new ChangeImplementingVo.ChangeRecordVo();
                recordVo.setRecordId(record.getId());
                recordVo.setChangeItemId(record.getChangeItemId());
                recordVo.setChangeId(record.getChangeId());
                recordVo.setChangeCode(record.getChangeCode());
                recordVo.setStage(record.getStage());
                recordVo.setProcessorStatus(record.getProcessorStatus());
                recordVo.setProcessor(record.getProcessor());
                recordVo.setProcessorTime(record.getProcessorTime());
                recordVo.setStep(record.getStep());
                recordVo.setOpinion(record.getOpinion());
                recordVo.setProcessOrder(record.getProcessOrder());
                recordVo.setNodeName(record.getNodeName());
                return recordVo;
            }).collect(Collectors.toList());
        }

        return recordVoList;
    }

    /**
     * 获取流程预览节点
     *
     * @param infoId 变更申请ID
     * @return 流程节点数据
     */
    @Override
    public FlowNodeVo getFlowPreview(Long infoId) {
        log.info("获取变更申请ID{}的流程预览节点", infoId);

        ChangeItem item = changeItemMapper.selectOne(new LambdaQueryWrapper<ChangeItem>().eq(ChangeItem::getChangeId, infoId));
        if (item == null) {
            log.warn("未找到变更申请ID{}对应的变更项", infoId);
            return null;
        }

        if (item.getInstanceId() == null) {
            log.warn("变更申请ID{}对应的变更项没有实例ID", infoId);
            return null;
        }

        try {
            // 调用工作流API获取流程实例数据
            FlowInstanceResp resp = smartFlowUtils.getFlowInstanceById(item.getInstanceId(), null);
            if (resp == null || resp.getData() == null) {
                log.warn("获取流程实例数据失败，实例ID: {}", item.getInstanceId());
                return null;
            }

            // 获取预览数据字符串
            String previewData = resp.getData().getPreviewData();
            if (StringUtils.isEmpty(previewData)) {
                log.warn("流程实例{}的预览数据为空", item.getInstanceId());
                return null;
            }
            return JsonUtils.parseObject(previewData, FlowNodeVo.class);
        } catch (Exception e) {
            log.error("获取流程预览节点失败，变更申请ID: {}, 异常: {}", infoId, e.getMessage(), e);
            return new FlowNodeVo();
        }
    }

    @Override
    public ChangeRecord getRecord(Long recordId) {
        ChangeRecord record = changeRecordMapper.selectById(recordId);
        if (ObjectUtil.isNull(record)) {
            return null;
        }
        String staffId = LoginHelper.getLoginUser().getStaffId();
        if (!record.getProcessor().getStaffId().equals(staffId)) {
            throw new ServiceException(MessageUtils.message("change.no.permission.approval", record.getChangeCode()));
        }
        return record;
    }
}
