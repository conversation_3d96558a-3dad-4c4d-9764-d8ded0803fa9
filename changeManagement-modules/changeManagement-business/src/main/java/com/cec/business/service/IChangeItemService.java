package com.cec.business.service;

import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.bo.ChangeItemBo;
import com.cec.business.domain.vo.ChangeStatisticsVo;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.mybatis.core.page.PageQuery;

import java.util.Collection;
import java.util.List;

/**
 * 变更申请记录Service接口
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface IChangeItemService {

    /**
     * 查询变更申请记录
     *
     * @param id 主键
     * @return 变更申请记录
     */
    ChangeItemVo queryById(Long id);

    /**
     * 分页查询变更申请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请记录分页列表
     */
    TableDataInfo<ChangeItemVo> queryPageList(ChangeItemBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的变更申请记录列表
     *
     * @param bo 查询条件
     * @return 变更申请记录列表
     */
    List<ChangeItemVo> queryList(ChangeItemBo bo);

    /**
     * 新增变更申请记录
     *
     * @param bo 变更申请记录
     * @return 是否新增成功
     */
    Boolean insertByBo(ChangeItemBo bo);

    /**
     * 修改变更申请记录
     *
     * @param bo 变更申请记录
     * @return 是否修改成功
     */
    Boolean updateByBo(ChangeItemBo bo);

    /**
     * 校验并批量删除变更申请记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 获取变更统计数据
     *
     * @param dateStart yyMMdd
     * @param dateEnd yyMMdd
     * @param teamId    团队ID
     * @param stages    阶段数组
     * @param pageQuery 分页参数
     * @return 变更统计数据
     */
    ChangeStatisticsVo getStatistics(String dateStart, String dateEnd, Long teamId, List<Integer> stages, PageQuery pageQuery);
}
