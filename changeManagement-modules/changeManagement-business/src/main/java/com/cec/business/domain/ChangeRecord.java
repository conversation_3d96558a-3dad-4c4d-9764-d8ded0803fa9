package com.cec.business.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.enums.LevelEnum;
import com.cec.business.domain.enums.ProcessorStatusEnum;
import com.cec.business.domain.enums.WhetherEnum;
import com.cec.business.domain.vo.LocationVo;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 变更申请记录流水对象 cm_change_record
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_change_record", autoResultMap = true)
public class ChangeRecord extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 变更记录ID
     */
    private Long changeItemId;

    /**
     * 变更ID
     */
    private Long changeId;

    /**
     * 变更编号
     */
    private String changeCode;

    /**
     * 变更标题
     */
    private String title;

    /**
     * 审批状态
     * 1-发起 2-已审批 3-已拒绝 4-待审批 5-已实施 6-已回滚 7-开始变更 8-通过 9-不通过 10-已取消
     */
    private ProcessorStatusEnum processorStatus;

    /**
     * 当前处理人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo processor;

    /**
     * 审批时间
     */
    private Date processorTime;

    /**
     * 变更组ID
     */
    private Long teamId;

    /**
     * 变更组名
     */
    private String teamName;

    /**
     * 是否紧急变更（1-是 2-否）
     */
    private WhetherEnum isUrgentChange;

    /**
     * 优先级(1-高 2-中 3-低)
     */
    private LevelEnum priority;

    /**
     * 审批意见/备注
     */
    private String opinion;

    /**
     * 是否需要加签（1-是 2-否）
     */
    private WhetherEnum isSealAddition;

    /**
     * 阶段
     * 1-草稿 2-已提交 3-待审批 4-已审批 5-实施中 6-已完成 7-已拒绝 8-已回滚 9-已取消 10-待验证
     */
    private ChangeStageEnum stage;

    /**
     * 步骤
     */
    private String step;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 会签类型（0-会签 1-或签）
     */
    private Integer countersignType;

    /**
     * 是否允许加签（0-否 1-是）
     */
    private Integer countersign;

    /**
     * smartFlow节点策略
     */
    private String strategy;

    /**
     * smartFlow待发送节点列表
     */
    private String nextList;

    /**
     * smartFlow当前任务信息
     */
    private String taskCurrent;

    /**
     * 顺序
     */
    private Integer processOrder;

    /**
     * 是否封网变更（1-是 2-否）
     */
    private WhetherEnum isNetworkFreezeChange;

    /**
     * 地点id
     */
    private Long locationId;

    /**
     * 地点名
     */
    private String locationName;

    /**
     * 申请人ID
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo requester;

    /**
     * 节点名称
     */
    private String nodeName;

    /**
     * 或签是否已被处理（1-是 2-否）
     */
    private WhetherEnum isOrSignProcessed;

    /**
     * 地点
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<LocationVo> locationVoList;
}
