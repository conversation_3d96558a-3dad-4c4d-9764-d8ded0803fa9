package com.cec.business.domain;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.cec.business.domain.enums.NetworkFreezeLevelEnum;
import com.cec.business.domain.enums.NetworkFreezeStageEnum;
import com.cec.common.mybatis.core.domain.BaseEntity;
import com.baomidou.mybatisplus.annotation.*;
import com.cec.system.domain.vo.UserVo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.Date;
import java.util.List;

/**
 * 封网申请记录对象 cm_network_freeze_item
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName(value = "cm_network_freeze_item", autoResultMap = true)
public class NetworkFreezeItem extends BaseEntity {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(value = "id")
    private Long id;

    /**
     * 封网ID
     */
    private Long freezeId;

    /**
     * 变更编号
     */
    private String freezeCode;

    /**
     * 封网名称
     */
    private String title;

    /**
     * 封网等级
     */
    private NetworkFreezeLevelEnum level;

    /**
     * 阶段(1-草稿/2-已提交/3已发布/4-已拒绝)
     */
    private NetworkFreezeStageEnum stage;

    /**
     * 当前处理人List
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<UserVo> processorList;

    /**
     * 流程实例id
     */
    private String instanceId;

    /**
     * smartFlow Variables
     */
    private String variables;

    /**
     * 封网开始时间
     */
    private Date freezeStartTime;

    /**
     * 封网结束时间
     */
    private Date freezeEndTime;

    /**
     * 申请人
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private UserVo requester;

    /**
     * 发布时间
     */
    private Date publishTime;

}
