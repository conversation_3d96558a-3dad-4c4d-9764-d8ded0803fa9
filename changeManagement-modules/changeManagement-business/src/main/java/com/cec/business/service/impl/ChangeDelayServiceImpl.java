package com.cec.business.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.cec.business.domain.ChangeDelay;
import com.cec.business.domain.ChangeInfo;
import com.cec.business.domain.bo.ChangeDelayBo;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.vo.ChangeDelayVo;
import com.cec.business.mapper.ChangeDelayMapper;
import com.cec.business.mapper.ChangeInfoMapper;
import com.cec.business.service.EmailService;
import com.cec.business.service.IChangeDelayService;
import com.cec.common.core.domain.model.LoginUser;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.vo.UserVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 变更延期记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-15
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class ChangeDelayServiceImpl implements IChangeDelayService {

    private final ChangeDelayMapper baseMapper;
    private final ChangeInfoMapper changeInfoMapper;
    private final EmailService emailService;

    /**
     * 查询变更延期记录
     *
     * @param id 主键
     * @return 变更延期记录
     */
    @Override
    public ChangeDelayVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询变更延期记录列表
     *
     * @param bo 查询条件
     * @return 变更延期记录分页列表
     */
    @Override
    public List<ChangeDelayVo> queryPageList(ChangeDelayBo bo) {
        LambdaQueryWrapper<ChangeDelay> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 查询符合条件的变更延期记录列表
     *
     * @param bo 查询条件
     * @return 变更延期记录列表
     */
    @Override
    public List<ChangeDelayVo> queryList(ChangeDelayBo bo) {
        LambdaQueryWrapper<ChangeDelay> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ChangeDelay> buildQueryWrapper(ChangeDelayBo bo) {
        LambdaQueryWrapper<ChangeDelay> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(ChangeDelay::getId);
        lqw.eq(bo.getChangeId() != null, ChangeDelay::getChangeId, bo.getChangeId());
        return lqw;
    }

    /**
     * 新增变更延期记录
     *
     * @param bo 变更延期记录
     * @return 是否新增成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertByBo(ChangeDelayBo bo) {
        ChangeDelay add = MapstructUtils.convert(bo, ChangeDelay.class);
        ChangeInfo info = changeInfoMapper.selectById(bo.getChangeId());
        validEntityBeforeSave(add, info);
        baseMapper.insert(add);
        info.setDelayEndTime(add.getScheduleEndTime());
        changeInfoMapper.updateById(info);
        sendEmailAsync(ChangeStageEnum.DELAYED, info.getId());
        return true;
    }

    /**
     * 修改变更延期记录
     *
     * @param bo 变更延期记录
     * @return 是否修改成功
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateByBo(ChangeDelayBo bo) {
        ChangeDelay update = MapstructUtils.convert(bo, ChangeDelay.class);
        ChangeInfo info = changeInfoMapper.selectById(bo.getChangeId());
        validEntityBeforeSave(update, info);
        info.setDelayEndTime(update.getScheduleEndTime());
        changeInfoMapper.updateById(info);
        baseMapper.updateById(update);
        return true;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ChangeDelay entity, ChangeInfo info) {
        //TODO 做一些数据校验,如唯一约束
        LoginUser loginUser = LoginHelper.getLoginUser();
        List<String> changeImplementerIds = info.getChangeImplementerList().stream().map(UserVo::getStaffId).toList();
        if (!changeImplementerIds.contains(loginUser.getStaffId())) {
            throw new ServiceException("您没有权限修改延期变更");
        }

        if (info.getDelayEndTime() != null && entity.getScheduleEndTime().before(info.getPlanTimeEnd())) {
            throw new ServiceException("延期变更结束时间不能早于计划结束时间");
        };
    }

    /**
     * 校验并批量删除变更延期记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 异步发送变更状态邮件
     *
     * @param stage      变更阶段
     * @param changeId   变更ID
     */
    private void sendEmailAsync(ChangeStageEnum stage, Long changeId) {
        CompletableFuture.runAsync(() -> {
            try {
                Thread.sleep(5000);
                emailService.sendEmailByChangeInfoId(stage, changeId);
                log.info("异步发送变更状态邮件成功, stage: {}, changeId: {}", stage, changeId);
            } catch (Exception e) {
                log.error("异步发送变更状态邮件失败, stage: {}, changeId: {}", stage, changeId, e);
            }
        });
    }
}
