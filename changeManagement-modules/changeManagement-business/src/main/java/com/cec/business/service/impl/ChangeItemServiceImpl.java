package com.cec.business.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.business.domain.ChangeItem;
import com.cec.business.domain.bo.ChangeItemBo;
import com.cec.business.domain.enums.ChangeStageEnum;
import com.cec.business.domain.vo.ChangeItemVo;
import com.cec.business.domain.vo.ChangeStatisticsVo;
import com.cec.business.mapper.ChangeItemMapper;
import com.cec.business.service.IChangeItemService;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 变更申请记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@RequiredArgsConstructor
@Service
public class ChangeItemServiceImpl implements IChangeItemService {

    private final ChangeItemMapper baseMapper;

    /**
     * 查询变更申请记录
     *
     * @param id 主键
     * @return 变更申请记录
     */
    @Override
    public ChangeItemVo queryById(Long id) {
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询变更申请记录列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 变更申请记录分页列表
     */
    @Override
    public TableDataInfo<ChangeItemVo> queryPageList(ChangeItemBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<ChangeItem> lqw = buildQueryWrapper(bo);
        Page<ChangeItemVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的变更申请记录列表
     *
     * @param bo 查询条件
     * @return 变更申请记录列表
     */
    @Override
    public List<ChangeItemVo> queryList(ChangeItemBo bo) {
        LambdaQueryWrapper<ChangeItem> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<ChangeItem> buildQueryWrapper(ChangeItemBo bo) {
        LambdaQueryWrapper<ChangeItem> lqw = Wrappers.lambdaQuery();
        lqw.orderByDesc(ChangeItem::getId);
//        lqw.eq(bo.getChangeId() != null, ChangeItem::getChangeId, bo.getChangeId());
//        lqw.eq(StringUtils.isNotBlank(bo.getChangeCode()), ChangeItem::getChangeCode, bo.getChangeCode());
//        lqw.eq(StringUtils.isNotBlank(bo.getTitle()), ChangeItem::getTitle, bo.getTitle());
//        lqw.eq(bo.getStage() != null, ChangeItem::getStage, bo.getStage());
//        lqw.eq(StringUtils.isNotBlank(bo.getProcessorId()), ChangeItem::getProcessorId, bo.getProcessorId());
//        lqw.like(StringUtils.isNotBlank(bo.getProcessorName()), ChangeItem::getProcessorName, bo.getProcessorName());
//        lqw.eq(bo.getTeamId() != null, ChangeItem::getTeamId, bo.getTeamId());
//        lqw.like(StringUtils.isNotBlank(bo.getTeamName()), ChangeItem::getTeamName, bo.getTeamName());
//        lqw.eq(bo.getIsUrgentChange() != null, ChangeItem::getIsUrgentChange, bo.getIsUrgentChange());
//        lqw.eq(bo.getPriority() != null, ChangeItem::getPriority, bo.getPriority());
        return lqw;
    }

    /**
     * 新增变更申请记录
     *
     * @param bo 变更申请记录
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(ChangeItemBo bo) {
        ChangeItem add = MapstructUtils.convert(bo, ChangeItem.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改变更申请记录
     *
     * @param bo 变更申请记录
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(ChangeItemBo bo) {
        ChangeItem update = MapstructUtils.convert(bo, ChangeItem.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(ChangeItem entity) {
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除变更申请记录信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if (isValid) {
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    @Override
    public ChangeStatisticsVo getStatistics(String dateStart, String dateEnd, Long teamId, List<Integer> stages, PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<ChangeItem> lqw = buildStatisticsQueryWrapper(dateStart, dateEnd, teamId, stages);

        // 查询所有符合条件的数据用于统计
        List<ChangeItem> allItems = baseMapper.selectList(lqw);

        // 并行计算统计指标
        StatisticsMetrics metrics = calculateStatisticsMetrics(allItems);

        // 查询分页数据
        Page<ChangeItem> page = queryPagedData(lqw, pageQuery);

        // 转换为VO对象
        List<ChangeStatisticsVo.ChangeItemVo> voList = convertToVoList(page.getRecords());

        // 构建返回对象
        return buildStatisticsResponse(metrics, page, voList);
    }

    /**
     * 构建统计查询条件
     */
    private LambdaQueryWrapper<ChangeItem> buildStatisticsQueryWrapper(String dateStart, String dateEnd, Long teamId, List<Integer> stages) {
        LambdaQueryWrapper<ChangeItem> lqw = Wrappers.lambdaQuery();

        addDateRangeConditions(lqw, dateStart, dateEnd);

        lqw.ne(ChangeItem::getStage, ChangeStageEnum.DRAFT);
        if (CollUtil.isNotEmpty(stages)) {
            lqw.in(ChangeItem::getStage, stages);
        }

        lqw.eq(teamId != null, ChangeItem::getTeamId, teamId);
        lqw.orderByDesc(ChangeItem::getId);

        return lqw;
    }

    /**
     * 添加日期范围查询条件
     */
    private void addDateRangeConditions(LambdaQueryWrapper<ChangeItem> lqw, String dateStart, String dateEnd) {
        if (StringUtils.isNotBlank(dateStart)) {
            LocalDateTime startDate = parseDate(dateStart).atTime(0, 0, 0);
            lqw.ge(ChangeItem::getCreateTime, startDate);
        }
        if (StringUtils.isNotBlank(dateEnd)) {
            LocalDateTime endDate = parseDate(dateEnd).atTime(23, 59, 59);
            lqw.le(ChangeItem::getCreateTime, endDate);
        }
    }

    /**
     * 解析日期字符串
     */
    private LocalDate parseDate(String dateStr) {
        return LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 计算统计指标
     */
    private StatisticsMetrics calculateStatisticsMetrics(List<ChangeItem> allItems) {
        CompletableFuture<Integer> totalCountFuture = CompletableFuture.supplyAsync(allItems::size);
        CompletableFuture<String> successRateFuture = CompletableFuture.supplyAsync(() -> calculateSuccessRate(allItems));
        CompletableFuture<String> avgProcessTimeFuture = CompletableFuture.supplyAsync(() -> calculateAvgProcessTime(allItems));

        // 等待所有CompletableFuture完成并获取结果
        CompletableFuture.allOf(totalCountFuture, successRateFuture, avgProcessTimeFuture).join();

        return new StatisticsMetrics(
            totalCountFuture.join(),
            successRateFuture.join(),
            avgProcessTimeFuture.join()
        );
    }

    /**
     * 计算成功率
     */
    private String calculateSuccessRate(List<ChangeItem> items) {
        if (CollUtil.isEmpty(items)) {
            return "0.00%";
        }

        long successCount = items.stream()
            .filter(item -> item.getStage() == ChangeStageEnum.COMPLETED)
            .count();

        return String.format("%.2f%%", (double) successCount / items.size() * 100);
    }

    /**
     * 计算平均处理时间
     */
    private String calculateAvgProcessTime(List<ChangeItem> items) {
        List<Long> processTimes = new ArrayList<>();

        for (ChangeItem item : items) {
            if (item.getCompleteTime() != null && item.getSubmitTime() != null) {
                long diff = item.getCompleteTime().getTime() - item.getSubmitTime().getTime();
                processTimes.add(diff / 1000); // 转换为秒
            }
        }

        if (processTimes.isEmpty()) {
            return "0";
        }

        double avgTime = processTimes.stream()
            .mapToLong(Long::longValue)
            .average()
            .orElse(0.0);

        return String.valueOf((int) avgTime);
    }

    /**
     * 查询分页数据
     */
    private Page<ChangeItem> queryPagedData(LambdaQueryWrapper<ChangeItem> lqw, PageQuery pageQuery) {
        if (pageQuery == null) {
            return baseMapper.selectPage(new Page<>(1, 5000), lqw);
        }
        return baseMapper.selectPage(pageQuery.build(), lqw);
    }

    /**
     * 转换为VO对象列表
     */
    private List<ChangeStatisticsVo.ChangeItemVo> convertToVoList(List<ChangeItem> items) {
        return items.stream()
            .map(this::convertToVo)
            .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 转换单个ChangeItem为VO对象
     */
    private ChangeStatisticsVo.ChangeItemVo convertToVo(ChangeItem item) {
        ChangeStatisticsVo.ChangeItemVo vo = new ChangeStatisticsVo.ChangeItemVo();
        vo.setId(item.getId());
        vo.setChangeId(item.getChangeId());
        vo.setChangeCode(item.getChangeCode());
        vo.setTitle(item.getTitle());
        vo.setStage(item.getStage());
        vo.setProcessorList(item.getProcessorList());
        vo.setTeamId(item.getTeamId());
        vo.setTeamName(item.getTeamName());
        vo.setIsUrgentChange(item.getIsUrgentChange());
        vo.setPriority(item.getPriority());
        vo.setInstanceId(item.getInstanceId());
        vo.setVariables(item.getVariables());
        vo.setCompleteTime(item.getCompleteTime());

        // 计算变更耗时
        String processTime = calculateProcessTime(item);
        vo.setProcessTime(processTime);
        vo.setFormattedProcessTime(vo.getFormattedProcessTime());

        return vo;
    }

    /**
     * 计算单个变更项的处理时间
     */
    private String calculateProcessTime(ChangeItem item) {
        if (item.getCompleteTime() != null && item.getSubmitTime() != null) {
            long diff = item.getCompleteTime().getTime() - item.getSubmitTime().getTime();
            return String.valueOf(diff / 1000); // 转换为秒
        }
        return null;
    }

    /**
     * 构建统计响应对象
     */
    private ChangeStatisticsVo buildStatisticsResponse(StatisticsMetrics metrics, Page<ChangeItem> page,
                                                       List<ChangeStatisticsVo.ChangeItemVo> voList) {
        ChangeStatisticsVo statisticsVo = new ChangeStatisticsVo();
        statisticsVo.setTotalCount(metrics.getTotalCount());
        statisticsVo.setSuccessRate(metrics.getSuccessRate());
        statisticsVo.setAvgProcessTime(metrics.getAvgProcessTime());

        // 创建适配VO的Page对象
        Page<ChangeStatisticsVo.ChangeItemVo> voPage = new Page<>(page.getCurrent(), page.getSize(), page.getTotal());
        voPage.setRecords(voList);

        statisticsVo.setData(TableDataInfo.build(voPage));
        return statisticsVo;
    }

    /**
     * 统计指标数据类
     */
    private static class StatisticsMetrics {
        private final int totalCount;
        private final String successRate;
        private final String avgProcessTime;

        public StatisticsMetrics(int totalCount, String successRate, String avgProcessTime) {
            this.totalCount = totalCount;
            this.successRate = successRate;
            this.avgProcessTime = avgProcessTime;
        }

        public int getTotalCount() {
            return totalCount;
        }

        public String getSuccessRate() {
            return successRate;
        }

        public String getAvgProcessTime() {
            return avgProcessTime;
        }
    }
}
