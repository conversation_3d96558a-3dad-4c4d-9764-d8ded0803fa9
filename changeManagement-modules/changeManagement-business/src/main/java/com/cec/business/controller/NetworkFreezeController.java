package com.cec.business.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.hutool.core.collection.CollUtil;
import com.cec.business.domain.NetworkFreezeArea;
import com.cec.business.domain.NetworkFreezeRecord;
import com.cec.business.domain.bo.NetworkFreezeInfoBo;
import com.cec.business.domain.bo.NetworkFreezeItemBo;
import com.cec.business.domain.bo.NetworkFreezeRecordBo;
import com.cec.business.domain.vo.FlowNodeVo;
import com.cec.business.domain.vo.NetworkFreezeInfoVo;
import com.cec.business.domain.vo.NetworkFreezeItemVo;
import com.cec.business.domain.vo.NetworkFreezeRecordVo;
import com.cec.business.service.INetworkFreezeService;
import com.cec.common.core.domain.R;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.core.validate.AddGroup;
import com.cec.common.core.validate.EditGroup;
import com.cec.common.excel.utils.ExcelUtil;
import com.cec.common.idempotent.annotation.RepeatSubmit;
import com.cec.common.json.utils.JsonUtils;
import com.cec.common.log.annotation.Log;
import com.cec.common.log.enums.BusinessType;
import com.cec.common.log.enums.LogTitleEnum;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.web.core.BaseController;
import com.cec.system.domain.vo.UserVo;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 封网申请
 *
 * <AUTHOR>
 * @date 2025-05-13
 */
@Validated
@RequiredArgsConstructor
@RestController
@RequestMapping("/business/networkFreeze")
@Slf4j
public class NetworkFreezeController extends BaseController {

    private final INetworkFreezeService networkFreezeService;

    /**
     * 查询封网申请列表
     */
    @SaCheckPermission("business:networkFreezeInfo:list")
    @GetMapping("/list")
    public TableDataInfo<NetworkFreezeItemVo> list(NetworkFreezeItemBo bo, PageQuery pageQuery) {
        return networkFreezeService.queryPageList(bo, pageQuery);
    }

    /**
     * 查询封网申请待审批列表（个人）
     */
    @SaCheckPermission("business:networkFreezeRecord:list")
    @GetMapping("/record/list")
    public TableDataInfo<NetworkFreezeRecordVo> list(NetworkFreezeRecordBo bo, PageQuery pageQuery) {
        return networkFreezeService.queryPageList(bo, pageQuery);
    }

    /**
     * 审批（同意或拒绝）
     */
    @SaCheckPermission("business:networkFreezeRecord:update")
    @Log(title = LogTitleEnum.NETWORK_FREEZE_APPROVAL, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping("/record")
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody NetworkFreezeRecordBo bo) {
        return toAjax(networkFreezeService.updateByBo(bo));
    }

    /**
     * 导出封网申请列表
     */
    @SaCheckPermission("business:networkFreezeInfo:query")
    @Log(title = LogTitleEnum.NETWORK_FREEZE_MANAGEMENT, businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public void export(NetworkFreezeItemBo bo, HttpServletResponse response) {
        try {
            // 创建一个足够大的分页参数，以获取所有数据
            PageQuery pageQuery = new PageQuery(5000, 1);

            // 获取封网申请列表数据
            TableDataInfo<NetworkFreezeItemVo> tableData = networkFreezeService.queryPageList(bo, pageQuery);
            List<NetworkFreezeItemVo> list = tableData.getRows();

            // 获取国际化资源
            MessageSource messageSource = SpringUtils.getBean(MessageSource.class);
            Locale locale = LocaleContextHolder.getLocale();

            // 定义字段映射关系(字段名和国际化key的映射)
            Map<String, String> fieldMap = new HashMap<>();
            // 基本字段
            fieldMap.put("freezeCode", "excel.header.freezeCode");
            fieldMap.put("title", "excel.header.freezeTitle");
            fieldMap.put("level", "excel.header.level");
            fieldMap.put("stage", "excel.header.freezeStage");
            fieldMap.put("processorList", "excel.header.processorName");
            fieldMap.put("createTime", "excel.header.createTime");
            fieldMap.put("freezeApps", "excel.header.freezeApps");
            fieldMap.put("periodAndArea", "excel.header.periodAndArea");
            fieldMap.put("requesterName", "excel.header.requesterName");
            fieldMap.put("publishTime", "excel.header.publishTime");

            // 获取要导出的字段列表
            List<String> selectFieldNameList;
            // 如果指定了要导出的字段，则使用指定的字段
            if (CollUtil.isNotEmpty(bo.getSelectFiledNameList())) {
                selectFieldNameList = bo.getSelectFiledNameList();
            } else {
                selectFieldNameList = new ArrayList<>(fieldMap.keySet());
            }

            // 准备表头
            List<List<String>> headList = new ArrayList<>();
            for (String fieldName : selectFieldNameList) {
                String i18nKey = fieldMap.getOrDefault(fieldName, "excel.header." + fieldName);
                String headerName = messageSource.getMessage(i18nKey, null, fieldName, locale);
                headList.add(Collections.singletonList(headerName));
            }

            // 准备数据
            List<List<Object>> dataList = new ArrayList<>();
            for (NetworkFreezeItemVo item : list) {
                List<Object> rowData = new ArrayList<>();
                for (String fieldName : selectFieldNameList) {
                    rowData.add(getFieldValue(messageSource, locale, item, fieldName));
                }
                dataList.add(rowData);
            }

            // 准备响应
            String sheetName = messageSource.getMessage("excel.sheet.freezeList", null, "封网列表", locale);
            String filename = ExcelUtil.encodingFilename(sheetName + "[Restricted]");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8");
            com.cec.common.core.utils.file.FileUtils.setAttachmentResponseHeader(response, filename);

            // 使用EasyExcel API导出
            try {
                com.alibaba.excel.EasyExcel.write(response.getOutputStream())
                    .autoCloseStream(false)
                    // 注册自定义列宽处理器
                    .registerWriteHandler(new com.cec.common.excel.handler.CustomColumnWidthStrategy())
                    // 注册自动换行样式处理器
                    .registerWriteHandler(new com.cec.common.excel.handler.AutoWrapStyleStrategy())
                    // 开始写入
                    .head(headList)
                    .sheet(sheetName)
                    .doWrite(dataList);
            } catch (Exception excelException) {
                log.error("EasyExcel写入异常", excelException);
                // 如果是列宽相关异常，尝试不使用自定义列宽策略重新导出
                if (excelException.getMessage() != null &&
                    (excelException.getMessage().contains("column width") ||
                     excelException.getMessage().contains("255 characters") ||
                     excelException.getMessage().contains("100 characters"))) {
                    log.warn("列宽超限，尝试使用默认列宽策略重新导出");
                    com.alibaba.excel.EasyExcel.write(response.getOutputStream())
                        .autoCloseStream(false)
                        // 仍然保留自动换行功能
                        .registerWriteHandler(new com.cec.common.excel.handler.AutoWrapStyleStrategy())
                        .head(headList)
                        .sheet(sheetName)
                        .doWrite(dataList);
                } else {
                    throw excelException;
                }
            }
        } catch (Exception e) {
            log.error("导出Excel异常", e);
            throw new RuntimeException("导出Excel异常", e);
        }
    }

    /**
     * 根据字段名获取对应字段值，并处理国际化
     *
     * @param messageSource 国际化资源
     * @param locale        当前语言环境
     * @param item          封网项数据
     * @param fieldName     字段名
     * @return 格式化后的字段值
     */
    private Object getFieldValue(MessageSource messageSource, Locale locale, NetworkFreezeItemVo item, String fieldName) {
        switch (fieldName) {
            case "freezeCode":
                return item.getFreezeCode();
            case "title":
                return item.getTitle();
            case "level":
                // 处理封网等级枚举值国际化
                return formatEnumValue(messageSource, locale, "enum.network.freeze.level.", item.getLevel());
            case "stage":
                // 处理封网阶段枚举值国际化
                return formatEnumValue(messageSource, locale, "enum.network.freeze.stage.", item.getStage());
            case "processorList":
                return Optional.ofNullable(item.getProcessorList()).orElse(Collections.emptyList())
                    .stream()
                    .map(UserVo::getStaffName)
                    .collect(Collectors.joining(","));
            case "createTime":
                return item.getCreateTime();
            case "freezeApps":
                return item.getFreezeApps();
            case "periodAndArea":
                // 处理封网时间段和区域信息
                return formatPeriodAndArea(item.getPeriodAndArea(), messageSource, locale);
            case "requesterName":
                // 处理申请人信息
                return Optional.ofNullable(item.getRequester())
                    .map(UserVo::getStaffName)
                    .orElse("");
            case "publishTime":
                // 处理发布时间
                return item.getPublishTime();
            default:
                // 对于未知字段，返回空值
                return "";
        }
    }

    /**
     * 格式化枚举值，处理国际化
     *
     * @param messageSource 国际化资源
     * @param locale        当前语言环境
     * @param keyPrefix     国际化key前缀
     * @param enumValue     枚举值
     * @return 国际化后的字符串
     */
    private String formatEnumValue(MessageSource messageSource, Locale locale, String keyPrefix, Enum<?> enumValue) {
        if (enumValue == null) {
            return "";
        }
        String key = keyPrefix + enumValue.name();
        return messageSource.getMessage(key, null, enumValue.toString(), locale);
    }

    /**
     * 格式化JSON数组字符串为逗号分隔的字符串
     *
     * @param jsonArrayStr JSON数组字符串
     * @return 格式化后的字符串
     */
    private String formatJsonArray(String jsonArrayStr) {
        if (StringUtils.isBlank(jsonArrayStr)) {
            return "";
        }
        try {
            List<String> list = JsonUtils.parseArray(jsonArrayStr, String.class);
            return CollUtil.join(list, ",");
        } catch (Exception e) {
            log.warn("解析JSON数组失败: {}", e.getMessage());
            return jsonArrayStr;
        }
    }

    /**
     * 获取封网申请详细信息
     *
     * @param id 主键
     */
    @SaCheckPermission("business:networkFreezeInfo:detail")
    @GetMapping("/{id}")
    public R<NetworkFreezeInfoVo> getInfo(@NotNull(message = "主键不能为空")
                                          @PathVariable Long id) {
        return R.ok(networkFreezeService.queryById(id));
    }

    /**
     * 新增封网申请
     */
    @SaCheckPermission("business:networkFreezeInfo:edit")
    @Log(title = LogTitleEnum.NETWORK_FREEZE_MANAGEMENT, businessType = BusinessType.INSERT)
    @RepeatSubmit()
    @PostMapping()
    public R<Long> add(@Validated(AddGroup.class) @RequestBody NetworkFreezeInfoBo bo) {
        return R.ok(networkFreezeService.insertByBo(bo));
    }

    /**
     * 修改封网申请
     */
    @SaCheckPermission("business:networkFreezeInfo:edit")
    @Log(title = LogTitleEnum.NETWORK_FREEZE_MANAGEMENT, businessType = BusinessType.UPDATE)
    @RepeatSubmit()
    @PutMapping()
    public R<Void> edit(@Validated(EditGroup.class) @RequestBody NetworkFreezeInfoBo bo) {
        return toAjax(networkFreezeService.updateByBo(bo));
    }

    /**
     * 删除封网申请
     *
     * @param ids 主键串
     */
    @SaCheckPermission("business:networkFreezeInfo:remove")
    @Log(title = LogTitleEnum.NETWORK_FREEZE_MANAGEMENT, businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public R<Void> remove(@NotEmpty(message = "主键不能为空")
                          @PathVariable Long[] ids) {
        return toAjax(networkFreezeService.deleteWithValidByIds(List.of(ids), true));
    }

    /**
     * 格式化封网时间段和区域信息
     *
     * @param areaList      封网区域列表
     * @param messageSource 国际化资源
     * @param locale        当前语言环境
     * @return 格式化后的字符串
     */
    private String formatPeriodAndArea(List<NetworkFreezeArea> areaList, MessageSource messageSource, Locale locale) {
        if (areaList == null || areaList.isEmpty()) {
            return "";
        }

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder result = new StringBuilder();

        String locationLabel = messageSource.getMessage("network.freeze.location", null, "地点", locale);
        String startTimeLabel = messageSource.getMessage("network.freeze.start.time", null, "开始时间", locale);
        String endTimeLabel = messageSource.getMessage("network.freeze.end.time", null, "结束时间", locale);

        // 判断是否支持英文
        boolean isEnglishLocale = isEnglishSupported(locale);

        for (NetworkFreezeArea area : areaList) {
            result.append("[");
            // 添加地点信息
            List<String> areaNames = getAreaNames(area, isEnglishLocale);
            if (areaNames != null && !areaNames.isEmpty()) {
                result.append(locationLabel).append(": ").append(String.join(", ", areaNames)).append("; ");
            }

            // 添加时间信息
            if (area.getStartTime() != null) {
                result.append(startTimeLabel).append(": ").append(dateFormat.format(area.getStartTime())).append("; ");
            }

            if (area.getEndTime() != null) {
                result.append(endTimeLabel).append(": ").append(dateFormat.format(area.getEndTime()));
            }

            result.append("]\n");
        }

        return result.toString().trim();
    }

    /**
     * 判断当前语言环境是否支持英文
     *
     * @param locale 当前语言环境
     * @return true如果支持英文，false否则
     */
    private boolean isEnglishSupported(Locale locale) {
        return locale != null && "en".equalsIgnoreCase(locale.getLanguage());
    }

    /**
     * 根据语言环境获取区域名称
     *
     * @param area            封网区域对象
     * @param isEnglishLocale 是否为英文语言环境
     * @return 区域名称列表
     */
    private List<String> getAreaNames(NetworkFreezeArea area, boolean isEnglishLocale) {
        if (isEnglishLocale && area.getAreaNameEn() != null && !area.getAreaNameEn().isEmpty()) {
            return area.getAreaNameEn();
        }
        return area.getAreaName();
    }

    /**
     * 获取封网-unfinished
     *
     * @return 封网信息列表
     */
    @GetMapping("/freezeUnfinished")
    public R<List<NetworkFreezeItemVo>> freezeUnfinished() {
        return R.ok(networkFreezeService.queryfreezeUnfinishedList());
    }

    /**
     * 获取审批明细
     *
     * @param recordId 审批申请ID
     * @return 审批记录明细列表
     */
    @GetMapping("/record/{recordId}")
    @SaCheckPermission("business:networkFreezeInfo:detail")
    public R<NetworkFreezeRecord> getRecord(@NotNull(message = "审批ID不能为空") @PathVariable Long recordId) {
        return R.ok(networkFreezeService.getRecord(recordId));
    }

    /**
     * 获取审批明细表
     *
     * @param infoId 变更申请ID
     * @return 审批记录明细列表
     */
    @GetMapping("/record/detail/{infoId}")
    @SaCheckPermission("business:networkFreezeInfo:detail")
    public R<List<NetworkFreezeRecordVo>> listRecord(@NotNull(message = "封网申请ID不能为空") @PathVariable Long infoId) {
        return R.ok(networkFreezeService.listRecord(infoId));
    }

    /**
     * 获取流程预览节点
     */
    @GetMapping("/flow/preview/{infoId}")
    @SaCheckPermission("business:networkFreezeInfo:detail")
    public R<FlowNodeVo> flowPreview(@NotNull(message = "封网申请ID不能为空") @PathVariable Long infoId) {
        return R.ok(networkFreezeService.getFlowPreview(infoId));
    }
}
