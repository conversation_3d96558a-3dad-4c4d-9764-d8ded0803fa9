package com.cec.business.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.cec.business.extra.SmartFlowUtils;
import com.cec.business.extra.req.SysUserSynchronizationReqVo;
import com.cec.business.extra.resp.SysUserSynchronizationResp;
import com.cec.common.core.config.CecSmartFlowConfig;
import com.cec.common.core.constant.TenantConstants;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.event.SysUserAllEvent;
import com.cec.system.domain.event.SysUserEvent;
import com.cec.system.mapper.SysUserMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * 用户同步到SmartFlow
 *
 * <AUTHOR> Li
 */
@RequiredArgsConstructor
@Service
@Slf4j
public class UserSyncSmartFlowService {

    private final SmartFlowUtils smartFlowUtils;
    private final CecSmartFlowConfig cecSmartFlowConfig;
    private final SysUserMapper sysUserMapper;

    @Async
    @EventListener
    public void syncUserIncrement(SysUserEvent sysUserEvent) {
        log.info("增量同步用户数据列表:{}", JSONUtil.toJsonPrettyStr(sysUserEvent));

        SysUserSynchronizationReqVo reqVo = new SysUserSynchronizationReqVo();
        reqVo.setSyncType(1);

        List<SysUserSynchronizationReqVo.SysUserSyncReqVo> list = sysUserEvent.getUsers().stream()
            .filter(user -> Objects.equals(user.getUserId(), TenantConstants.SUPER_ADMIN_ID))
            .filter(user -> Objects.nonNull(user.getDirectLeader()))
            .map(user -> {
                SysUserSynchronizationReqVo.SysUserSyncReqVo sysUserSyncReqVo = new SysUserSynchronizationReqVo.SysUserSyncReqVo();
                sysUserSyncReqVo.setBizLine(cecSmartFlowConfig.getParam().getBizLine());
                sysUserSyncReqVo.setEmail(user.getEmail());
                sysUserSyncReqVo.setExternalId(String.valueOf(user.getUserId()));
                sysUserSyncReqVo.setExternalOrgId(cecSmartFlowConfig.getParam().getExternalOrgId());
                sysUserSyncReqVo.setExternalRegionId(cecSmartFlowConfig.getParam().getExternalRegionId());
                sysUserSyncReqVo.setManagerNum(user.getDirectLeader().getStaffId());
                sysUserSyncReqVo.setOrgChartName(cecSmartFlowConfig.getParam().getDeptName());
                sysUserSyncReqVo.setRegion(cecSmartFlowConfig.getParam().getRegion());
                sysUserSyncReqVo.setStaffName(user.getStaffName());
                sysUserSyncReqVo.setStaffNum(user.getStaffId());
                sysUserSyncReqVo.setVersionId(512L);
                log.info("过滤后同步用户数据:{}", JSONUtil.toJsonPrettyStr(sysUserSyncReqVo));
                return sysUserSyncReqVo;
            }).toList();
        reqVo.setSysUserSyncReqVoList(list);

        SysUserSynchronizationResp resp = smartFlowUtils.syncUserData(reqVo);
        if (resp.getCode() == 200) {
            list.forEach(sysUserSyncReqVo -> {
                LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SysUser::getUserId, sysUserSyncReqVo.getExternalId());
                updateWrapper.set(SysUser::getSyncStatus, 1);
                sysUserMapper.update(null, updateWrapper);
            });
        }
    }

    @Async
    @EventListener
    public void syncUserAll(SysUserAllEvent sysUserAllEvent) {
        log.info("全量同步用户数据列表:{}", JSONUtil.toJsonPrettyStr(sysUserAllEvent));

        SysUserSynchronizationReqVo reqVo = new SysUserSynchronizationReqVo();
        reqVo.setSyncType(1);

        List<SysUserSynchronizationReqVo.SysUserSyncReqVo> list = sysUserAllEvent.getUsers().stream()
            .filter(user -> Objects.equals(user.getUserId(), TenantConstants.SUPER_ADMIN_ID))
            .filter(user -> Objects.nonNull(user.getDirectLeader()))
            .map(user -> {
                SysUserSynchronizationReqVo.SysUserSyncReqVo sysUserSyncReqVo = new SysUserSynchronizationReqVo.SysUserSyncReqVo();
                sysUserSyncReqVo.setBizLine(cecSmartFlowConfig.getParam().getBizLine());
                sysUserSyncReqVo.setEmail(user.getEmail());
                sysUserSyncReqVo.setExternalId(String.valueOf(user.getUserId()));
                sysUserSyncReqVo.setExternalOrgId(cecSmartFlowConfig.getParam().getExternalOrgId());
                sysUserSyncReqVo.setExternalRegionId(cecSmartFlowConfig.getParam().getExternalRegionId());
                sysUserSyncReqVo.setManagerNum(user.getDirectLeader().getStaffId());
                sysUserSyncReqVo.setOrgChartName(cecSmartFlowConfig.getParam().getDeptName());
                sysUserSyncReqVo.setRegion(cecSmartFlowConfig.getParam().getRegion());
                sysUserSyncReqVo.setStaffName(user.getStaffName());
                sysUserSyncReqVo.setStaffNum(user.getStaffId());
                sysUserSyncReqVo.setVersionId(512L);
                log.info("过滤后同步用户数据:{}", JSONUtil.toJsonPrettyStr(sysUserSyncReqVo));
                return sysUserSyncReqVo;
            }).toList();
        reqVo.setSysUserSyncReqVoList(list);

        SysUserSynchronizationResp resp = smartFlowUtils.syncUserData(reqVo);
        if (resp.getCode() == 200) {
            list.forEach(sysUserSyncReqVo -> {
                LambdaUpdateWrapper<SysUser> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(SysUser::getUserId, sysUserSyncReqVo.getExternalId());
                updateWrapper.set(SysUser::getSyncStatus, 1);
                sysUserMapper.update(null, updateWrapper);
            });
        }
    }

}
