package com.cec.system.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.crypto.digest.BCrypt;
import cn.hutool.http.HtmlUtil;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.utils.MessageUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StreamUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.core.utils.ValidatorUtils;
import com.cec.common.core.validate.ImportGroup;
import com.cec.common.excel.core.ExcelListener;
import com.cec.common.excel.core.ExcelResult;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.bo.SysUserBo;
import com.cec.system.domain.vo.SysUserImportVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.domain.vo.UserVo;
import com.cec.system.service.ISysConfigService;
import com.cec.system.service.ISysUserService;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 系统用户自定义导入
 *
 * <AUTHOR> Li
 */
@Slf4j
public class SysUserImportListener extends AnalysisEventListener<SysUserImportVo> implements ExcelListener<SysUserImportVo> {

    private final ISysUserService userService;
    private final String password;

    private final Boolean isUpdateSupport;

    private final Long operUserId;

    private int successNum = 0;
    private int failureNum = 0;
    private int totalProcessedCount = 0; // 实际处理的总行数
    private final StringBuilder successMsg = new StringBuilder();
    private final StringBuilder failureMsg = new StringBuilder();
    private static final String STAFFID_PATTERN = "^[a-zA-Z]+[a-zA-Z0-9]*$";

    // 是否是关系处理阶段
    private final boolean isRelationshipPhase;

    // 缓存所有导入的用户，用于关系处理
    private final List<SysUserImportVo> allImportedUsers = new ArrayList<>();

    // 缓存StaffId到UserVo的映射
    private Map<String, UserVo> staffIdToUserVoMap;

    public SysUserImportListener(Boolean isUpdateSupport) {
        this(isUpdateSupport, false);
    }

    public SysUserImportListener(Boolean isUpdateSupport, boolean isRelationshipPhase) {
        String initPassword = SpringUtils.getBean(ISysConfigService.class).selectConfigByKey("sys.user.initPassword");
        this.userService = SpringUtils.getBean(ISysUserService.class);
        this.password = BCrypt.hashpw(initPassword);
        this.isUpdateSupport = isUpdateSupport;
        this.operUserId = LoginHelper.getUserId();
        this.isRelationshipPhase = isRelationshipPhase;
        log.info("创建SysUserImportListener: isUpdateSupport={}, isRelationshipPhase={}", isUpdateSupport, isRelationshipPhase);
    }

    /**
     * 初始化staffId到UserVo的映射
     */
    private void initStaffIdToUserVoMap() {
        staffIdToUserVoMap = new HashMap<>();
        List<SysUserVo> allUsers = userService.selectUserList(new SysUserBo());
        for (SysUserVo user : allUsers) {
            staffIdToUserVoMap.put(user.getStaffId(), user.getUserVo());
        }
        log.info("成功加载 " + staffIdToUserVoMap.size() + " 个用户信息到映射表");
    }

    @Override
    public void invoke(SysUserImportVo userVo, AnalysisContext context) {
        totalProcessedCount++; // 统计实际处理的行数
        log.info("处理Excel行: staffId={}, isRelationshipPhase={}, totalProcessedCount={}", userVo.getStaffId(), isRelationshipPhase, totalProcessedCount);

        // 所有导入都需要处理基本信息
        processBasicInfo(userVo);

        // 如果是关系处理阶段，额外缓存用户信息用于后续处理关系
        if (isRelationshipPhase) {
            allImportedUsers.add(userVo);
            log.info("缓存用户关系数据: staffId={}, leaderStaffId={}, managerStaffId={}, directLeaderStaffId={}",
                userVo.getStaffId(), userVo.getLeaderStaffId(), userVo.getManagerStaffId(), userVo.getDirectLeaderStaffId());
        }
    }

    /**
     * 处理基本信息导入
     */
    private void processBasicInfo(SysUserImportVo userVo) {
        SysUserVo sysUser = this.userService.selectUserByStaffId(userVo.getStaffId());
        try {
            // 验证是否存在这个用户
            if (ObjectUtil.isNull(sysUser)) {
                SysUserBo user = BeanUtil.toBean(userVo, SysUserBo.class);
                ValidatorUtils.validate(user, ImportGroup.class);
                if (StringUtils.isNotEmpty(user.getEmail()) && !userService.checkEmailUnique(user)) {
                    throw new ServiceException(MessageUtils.message("user.add.fail.email.exist", user.getStaffId()));
                }

                // 校验staffId规则
                String staffId = user.getStaffId();
                if (StringUtils.isNotEmpty(staffId) && !staffId.matches(STAFFID_PATTERN)) {
                    throw new ServiceException(MessageUtils.message("user.staffid.rule.invalid"));
                }

                user.setPassword(password);
                user.setCreateBy(operUserId);
                userService.insertUser(user, 1);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getStaffId()).append(" 导入成功");
            } else if (isUpdateSupport) {
                Long userId = sysUser.getUserId();
                SysUserBo user = BeanUtil.toBean(userVo, SysUserBo.class);
                user.setUserId(userId);
                ValidatorUtils.validate(user);
                userService.checkUserAllowed(user.getUserId());
                userService.checkUserDataScope(user.getUserId());
                user.setUpdateBy(operUserId);
                userService.updateUser(user);
                successNum++;
                successMsg.append("<br/>").append(successNum).append("、账号 ").append(user.getStaffId()).append(" 更新成功");
            } else {
                failureNum++;
                failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(sysUser.getStaffId()).append(" 已存在");
            }
        } catch (Exception e) {
            failureNum++;
            String msg = "<br/>" + failureNum + "、账号 " + HtmlUtil.cleanHtmlTag(userVo.getStaffId()) + " 导入失败：";
            String message = e.getMessage();
            if (e instanceof ConstraintViolationException cvException) {
                message = StreamUtils.join(cvException.getConstraintViolations(), ConstraintViolation::getMessage, ", ");
            }
            failureMsg.append(msg).append(message);
            log.error(msg, e);
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (isRelationshipPhase) {
            // 初始化staffId到UserVo的映射，确保包含最新导入的用户
            initStaffIdToUserVoMap();
            // 关系处理阶段
            log.info("开始处理用户关系...");
            processRelationships();
        }
    }

    /**
     * 处理用户关系
     */
    private void processRelationships() {
        log.info("开始处理用户关系，共 {} 条记录", allImportedUsers.size());

        if (allImportedUsers.isEmpty()) {
            log.warn("没有要处理的用户关系数据");
            return;
        }

        // 输出全部staffIdToUserVoMap中的key，用于调试
        log.info("staffIdToUserVoMap中的StaffId列表: {}", staffIdToUserVoMap.keySet());

        int relationCount = 0;

        for (SysUserImportVo userVo : allImportedUsers) {
            try {
                // 检查是否有关系数据
                boolean hasRelationship = false;
                if (userVo.getLeaderStaffId() != null && !userVo.getLeaderStaffId().trim().isEmpty()) {
                    hasRelationship = true;
                }
                if (userVo.getManagerStaffId() != null && !userVo.getManagerStaffId().trim().isEmpty()) {
                    hasRelationship = true;
                }
                if (userVo.getDirectLeaderStaffId() != null && !userVo.getDirectLeaderStaffId().trim().isEmpty()) {
                    hasRelationship = true;
                }

                if (!hasRelationship) {
                    log.info("用户 {} 没有关系数据，跳过", userVo.getStaffId());
                    continue;
                }

                SysUserVo sysUser = userService.selectUserByStaffId(userVo.getStaffId());
                if (ObjectUtil.isNull(sysUser)) {
                    failureNum++;
                    String msg = "账号 " + userVo.getStaffId() + " 不存在，无法建立关系";
                    failureMsg.append("<br/>").append(failureNum).append("、").append(msg);
                    log.error(msg);
                    continue;
                }

                log.info("处理用户 {} 的关系数据: leaderStaffId={}, managerStaffId={}, directLeaderStaffId={}",
                    userVo.getStaffId(), userVo.getLeaderStaffId(), userVo.getManagerStaffId(), userVo.getDirectLeaderStaffId());

                SysUserBo user = new SysUserBo();
                user.setUserId(sysUser.getUserId());
                relationCount++;

                // 设置团队领导关系
                if (userVo.getLeaderStaffId() != null && !userVo.getLeaderStaffId().trim().isEmpty()) {
                    if (staffIdToUserVoMap.containsKey(userVo.getLeaderStaffId())) {
                        user.setTeamLeader(staffIdToUserVoMap.get(userVo.getLeaderStaffId()));
                        log.info("用户 {} 的Leader设置为: {}", userVo.getStaffId(), userVo.getLeaderStaffId());
                    } else {
                        log.warn("找不到Leader: {} 对应的用户", userVo.getLeaderStaffId());
                    }
                }

                // 设置团队经理关系
                if (userVo.getManagerStaffId() != null && !userVo.getManagerStaffId().trim().isEmpty()) {
                    if (staffIdToUserVoMap.containsKey(userVo.getManagerStaffId())) {
                        user.setTeamManager(staffIdToUserVoMap.get(userVo.getManagerStaffId()));
                        log.info("用户 {} 的Manager设置为: {}", userVo.getStaffId(), userVo.getManagerStaffId());
                    } else {
                        log.warn("找不到Manager: {} 对应的用户", userVo.getManagerStaffId());
                    }
                }

                // 设置直接领导关系
                if (userVo.getDirectLeaderStaffId() != null && !userVo.getDirectLeaderStaffId().trim().isEmpty()) {
                    if (staffIdToUserVoMap.containsKey(userVo.getDirectLeaderStaffId())) {
                        user.setDirectLeader(staffIdToUserVoMap.get(userVo.getDirectLeaderStaffId()));
                        log.info("用户 {} 的DirectLeader设置为: {}", userVo.getStaffId(), userVo.getDirectLeaderStaffId());
                    } else {
                        log.warn("找不到Direct Leader: {} 对应的用户", userVo.getDirectLeaderStaffId());
                    }
                }

                user.setUpdateBy(operUserId);

                // 打印完整的user对象用于调试
                log.info("准备更新用户关系: userId={}, teamLeader={}, teamManager={}, directLeader={}",
                    user.getUserId(),
                    user.getTeamLeader() != null ? user.getTeamLeader().getStaffId() : "null",
                    user.getTeamManager() != null ? user.getTeamManager().getStaffId() : "null",
                    user.getDirectLeader() != null ? user.getDirectLeader().getStaffId() : "null");

                int result = userService.updateUserRelationships(user);
                log.info("更新用户关系结果: {}", result > 0 ? "成功" : "失败");

                if (result > 0) {
                    successMsg.append("<br/>").append(successNum).append("、账号 ").append(sysUser.getStaffId()).append(" 关系更新成功");
                } else {
                    failureMsg.append("<br/>").append(failureNum).append("、账号 ").append(sysUser.getStaffId()).append(" 关系更新失败");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、账号 " + HtmlUtil.cleanHtmlTag(userVo.getStaffId()) + " 关系更新失败：";
                failureMsg.append(msg).append(e.getMessage());
                log.error("更新用户关系异常", e);
            }
        }

        log.info("用户关系处理完成: 总数={}, 有关系数据={}, 成功={}, 失败={}",
            allImportedUsers.size(), relationCount, successNum, failureNum);
    }

    @Override
    public ExcelResult<SysUserImportVo> getExcelResult() {
        return new ExcelResult<>() {

            @Override
            public String getAnalysis() {
                // 使用实际处理的行数作为总数
                String resultMsg = MessageUtils.message("import.result", totalProcessedCount, successNum, totalProcessedCount - successNum);

                StringBuilder result = new StringBuilder(resultMsg);

                if (failureNum > 0) {
                    // 将失败信息改为每行显示一个失败记录，格式为：失败StaffID: xxx, 原因: xxx
                    String errorMsg = failureMsg.toString();
                    // 移除<br/>标签和数字序号
                    errorMsg = errorMsg.replace("<br/>", "\n");
                    // 使用正则表达式匹配"账号 xxx 导入失败：yyy"或"账号 xxx 关系更新失败：yyy"格式
                    java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("\\d+、账号\\s+([^\\s]+)\\s+(?:导入|关系更新|已存在)(?:失败)?(?:：)?(.*)");
                    java.util.regex.Matcher matcher = pattern.matcher(errorMsg);

                    while (matcher.find()) {
                        String staffId = matcher.group(1);
                        String reason = matcher.group(2);
                        if (reason == null || reason.trim().isEmpty()) {
                            reason = MessageUtils.message("user.import.exists");
                        }
                        // 使用国际化消息
                        String failMessage = MessageUtils.message("import.fail.staffId", staffId, reason);
                        result.append("\n").append(failMessage);
                    }
                }

                return result.toString();
            }

            @Override
            public List<SysUserImportVo> getList() {
                return null;
            }

            @Override
            public List<String> getErrorList() {
                return null;
            }
        };
    }
}
