package com.cec.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.cec.common.core.constant.CacheNames;
import com.cec.common.core.constant.SystemConstants;
import com.cec.common.core.domain.dto.UserDTO;
import com.cec.common.core.exception.ServiceException;
import com.cec.common.core.service.UserService;
import com.cec.common.core.utils.MapstructUtils;
import com.cec.common.core.utils.ObjectUtils;
import com.cec.common.core.utils.SpringUtils;
import com.cec.common.core.utils.StreamUtils;
import com.cec.common.core.utils.StringUtils;
import com.cec.common.mybatis.core.page.PageQuery;
import com.cec.common.mybatis.core.page.TableDataInfo;
import com.cec.common.satoken.utils.LoginHelper;
import com.cec.system.domain.SysDept;
import com.cec.system.domain.SysRole;
import com.cec.system.domain.SysUser;
import com.cec.system.domain.SysUserPost;
import com.cec.system.domain.SysUserRole;
import com.cec.system.domain.bo.SysUserBo;
import com.cec.system.domain.event.SysUserEvent;
import com.cec.system.domain.vo.SysPostVo;
import com.cec.system.domain.vo.SysRoleVo;
import com.cec.system.domain.vo.SysUserExportVo;
import com.cec.system.domain.vo.SysUserVo;
import com.cec.system.mapper.SysDeptMapper;
import com.cec.system.mapper.SysPostMapper;
import com.cec.system.mapper.SysRoleMapper;
import com.cec.system.mapper.SysUserMapper;
import com.cec.system.mapper.SysUserPostMapper;
import com.cec.system.mapper.SysUserRoleMapper;
import com.cec.system.service.ISysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.cec.common.core.constant.SystemConstants.USER_ENABLE;

/**
 * 用户 业务层处理
 *
 * <AUTHOR> Li
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class SysUserServiceImpl implements ISysUserService, UserService {

    private final SysUserMapper baseMapper;
    private final SysDeptMapper deptMapper;
    private final SysRoleMapper roleMapper;
    private final SysPostMapper postMapper;
    private final SysUserRoleMapper userRoleMapper;
    private final SysUserPostMapper userPostMapper;

    @Override
    public TableDataInfo<SysUserVo> selectPageUserList(SysUserBo user, PageQuery pageQuery) {
        // 构建查询条件
        LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery(SysUser.class);
        lqw.eq(SysUser::getDelFlag, SystemConstants.NORMAL)
            .eq(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId())
            // .ne(SysUser::getUserId, SystemConstants.SUPER_ADMIN_ID)
            .like(StringUtils.isNotBlank(user.getStaffName()), SysUser::getStaffName, user.getStaffName())
            .eq(StringUtils.isNotBlank(user.getStatus()), SysUser::getStatus, user.getStatus())
            .like(StringUtils.isNotBlank(user.getStaffId()), SysUser::getStaffId, user.getStaffId())
            .orderByDesc(SysUser::getUserId);

        // 处理多个部门ID条件
        if (ObjectUtil.isNotNull(user.getDeptIds()) && user.getDeptIds().length > 0) {
            List<Long> allDeptIds = new ArrayList<>();
            for (Long deptId : user.getDeptIds()) {
                List<SysDept> deptList = deptMapper.selectListByParentId(deptId);
                List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                ids.add(deptId);
                allDeptIds.addAll(ids);
            }
            lqw.in(SysUser::getDeptId, allDeptIds);
        } else if (ObjectUtil.isNotNull(user.getDeptId())) {
            // 兼容单个部门查询
            List<SysDept> deptList = deptMapper.selectListByParentId(Long.valueOf(user.getDeptId()));
            List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
            ids.add(Long.valueOf(user.getDeptId()));
            lqw.in(SysUser::getDeptId, ids);
        }

        // 处理多个角色ID查询条件
        List<Long> userIdsFromRoles = new ArrayList<>();
        boolean hasRoleFilter = false;

        if (ObjectUtil.isNotNull(user.getRoleIds()) && user.getRoleIds().length > 0) {
            hasRoleFilter = true;
            for (Long roleId : user.getRoleIds()) {
                List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(roleId);
                if (CollUtil.isNotEmpty(userIds)) {
                    userIdsFromRoles.addAll(userIds);
                }
            }
        } else if (ObjectUtil.isNotNull(user.getRoleId())) {
            // 兼容单个角色查询
            hasRoleFilter = true;
            List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
            if (CollUtil.isNotEmpty(userIds)) {
                userIdsFromRoles.addAll(userIds);
            }
        }

        // 如果有角色过滤条件
        if (hasRoleFilter) {
            if (CollUtil.isEmpty(userIdsFromRoles)) {
                // 如果没有用户拥有这些角色，则返回空结果
                return TableDataInfo.build(new Page<>());
            }
            lqw.in(SysUser::getUserId, userIdsFromRoles);
        }

        // 处理排除的用户ID
        if (StringUtils.isNotBlank(user.getExcludeUserIds())) {
            lqw.notIn(SysUser::getUserId, StringUtils.splitTo(user.getExcludeUserIds(), Convert::toLong));
        }

        // 执行分页查询
        Page<SysUser> page = baseMapper.selectPage(pageQuery.build(), lqw);

        // 转换结果
        Page<SysUserVo> pageVo = new Page<>();
        BeanUtil.copyProperties(page, pageVo, "records");
        pageVo.setRecords(BeanUtil.copyToList(page.getRecords(), SysUserVo.class));

        // 获取用户角色信息
        if (CollUtil.isNotEmpty(pageVo.getRecords())) {
            // 获取所有用户ID
            List<Long> userIds = StreamUtils.toList(pageVo.getRecords(), SysUserVo::getUserId);
            // 批量查询所有用户角色关联
            List<SysUserRole> userRoles = userRoleMapper.selectList(
                new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, userIds));

            if (CollUtil.isNotEmpty(userRoles)) {
                // 获取所有角色ID
                List<Long> roleIds = StreamUtils.toList(userRoles, SysUserRole::getRoleId);
                // 批量查询所有角色信息
                List<SysRoleVo> roles = roleMapper.selectVoList(
                    new LambdaQueryWrapper<SysRole>().in(SysRole::getRoleId, roleIds));

                // 构建用户ID到角色列表的映射
                Map<Long, List<SysRoleVo>> userRoleMap = new java.util.HashMap<>();
                for (SysUserRole ur : userRoles) {
                    Long userId = ur.getUserId();
                    Long roleId = ur.getRoleId();
                    // 找到对应的角色
                    SysRoleVo role = roles.stream()
                        .filter(r -> r.getRoleId().equals(roleId))
                        .findFirst()
                        .orElse(null);

                    if (role != null) {
                        userRoleMap.computeIfAbsent(userId, k -> new ArrayList<>()).add(role);
                    }
                }

                // 填充用户角色信息
                for (SysUserVo userVo : pageVo.getRecords()) {
                    userVo.setRoles(userRoleMap.getOrDefault(userVo.getUserId(), new ArrayList<>()));
                }
            }
        }

        return TableDataInfo.build(pageVo);
    }

    /**
     * 根据条件分页查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserExportVo> selectUserExportList(SysUserBo user) {
        return baseMapper.selectUserExportList(this.buildQueryWrapper(user));
    }

    private Wrapper<SysUser> buildQueryWrapper(SysUserBo user) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", SystemConstants.NORMAL)
            .eq(ObjectUtil.isNotNull(user.getUserId()), "u.user_id", user.getUserId())
            .like(StringUtils.isNotBlank(user.getStaffName()), "u.staff_name", user.getStaffName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .like(StringUtils.isNotBlank(user.getStaffId()), "u.staff_id", user.getStaffId());

        // 处理多个部门ID条件
        if (ObjectUtil.isNotNull(user.getDeptIds()) && user.getDeptIds().length > 0) {
            wrapper.and(w -> {
                List<Long> allDeptIds = new ArrayList<>();
                for (Long deptId : user.getDeptIds()) {
                    List<SysDept> deptList = deptMapper.selectListByParentId(deptId);
                    List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                    ids.add(deptId);
                    allDeptIds.addAll(ids);
                }
                w.in("u.dept_id", allDeptIds);
            });
        } else if (ObjectUtil.isNotNull(user.getDeptId())) {
            // 兼容单个部门查询
            wrapper.and(w -> {
                List<SysDept> deptList = deptMapper.selectListByParentId(Long.valueOf(user.getDeptId()));
                List<Long> ids = StreamUtils.toList(deptList, SysDept::getDeptId);
                ids.add(Long.valueOf(user.getDeptId()));
                w.in("u.dept_id", ids);
            });
        }

        // 处理多个角色ID查询条件
        List<Long> userIdsFromRoles = new ArrayList<>();
        boolean hasRoleFilter = false;

        if (ObjectUtil.isNotNull(user.getRoleIds()) && user.getRoleIds().length > 0) {
            hasRoleFilter = true;
            for (Long roleId : user.getRoleIds()) {
                List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(roleId);
                if (CollUtil.isNotEmpty(userIds)) {
                    userIdsFromRoles.addAll(userIds);
                }
            }
        } else if (ObjectUtil.isNotNull(user.getRoleId())) {
            // 兼容单个角色查询
            hasRoleFilter = true;
            List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
            if (CollUtil.isNotEmpty(userIds)) {
                userIdsFromRoles.addAll(userIds);
            }
        }

        // 如果有角色过滤条件且有匹配用户
        if (hasRoleFilter && CollUtil.isNotEmpty(userIdsFromRoles)) {
            wrapper.in("u.user_id", userIdsFromRoles);
        } else if (hasRoleFilter) {
            // 如果有角色过滤条件但没有匹配用户，添加一个不可能满足的条件
            wrapper.eq("u.user_id", -1);
        }

        wrapper.orderByAsc("u.user_id");
        if (StringUtils.isNotBlank(user.getExcludeUserIds())) {
            wrapper.notIn("u.user_id", StringUtils.splitTo(user.getExcludeUserIds(), Convert::toLong));
        }
        return wrapper;
    }

    /**
     * 根据条件分页查询已分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectAllocatedList(SysUserBo user, PageQuery pageQuery) {
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", SystemConstants.NORMAL)
            .eq(ObjectUtil.isNotNull(user.getRoleId()), "r.role_id", user.getRoleId())
            .like(StringUtils.isNotBlank(user.getStaffName()), "u.staff_name", user.getStaffName())
            .eq(StringUtils.isNotBlank(user.getStatus()), "u.status", user.getStatus())
            .orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectAllocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 根据条件分页查询未分配用户角色列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public TableDataInfo<SysUserVo> selectUnallocatedList(SysUserBo user, PageQuery pageQuery) {
        List<Long> userIds = userRoleMapper.selectUserIdsByRoleId(user.getRoleId());
        QueryWrapper<SysUser> wrapper = Wrappers.query();
        wrapper.eq("u.del_flag", SystemConstants.NORMAL)
            .and(w -> w.ne("r.role_id", user.getRoleId()).or().isNull("r.role_id"))
            .notIn(CollUtil.isNotEmpty(userIds), "u.user_id", userIds)
            .like(StringUtils.isNotBlank(user.getStaffName()), "u.staff_name", user.getStaffName())
            .orderByAsc("u.user_id");
        Page<SysUserVo> page = baseMapper.selectUnallocatedList(pageQuery.build(), wrapper);
        return TableDataInfo.build(page);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByUserName(String userName) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getUserName, userName));
    }

    /**
     * 通过用户名查询用户
     *
     * @param staffId 用户名
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByStaffId(String staffId) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getStaffId, staffId));
    }

    /**
     * 通过手机号查询用户
     *
     * @param phonenumber 手机号
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserByPhonenumber(String phonenumber) {
        return baseMapper.selectVoOne(new LambdaQueryWrapper<SysUser>().eq(SysUser::getPhonenumber, phonenumber));
    }

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    @Override
    public SysUserVo selectUserById(Long userId) {
        SysUserVo user = baseMapper.selectVoById(userId);
        if (ObjectUtil.isNull(user)) {
            return user;
        }
        user.setRoles(roleMapper.selectRolesByUserId(user.getUserId()));
        return user;
    }

    /**
     * 通过用户ID串查询用户
     *
     * @param userIds 用户ID串
     * @param deptId  部门id
     * @return 用户列表信息
     */
    @Override
    public List<SysUserVo> selectUserByIds(List<Long> userIds, Long deptId) {
        return baseMapper.selectUserList(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getStatus, USER_ENABLE)
            .eq(ObjectUtil.isNotNull(deptId), SysUser::getDeptId, deptId)
            .in(CollUtil.isNotEmpty(userIds), SysUser::getUserId, userIds));
    }

    /**
     * 查询用户所属角色组
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public String selectUserRoleGroup(Long userId) {
        List<SysRoleVo> list = roleMapper.selectRolesByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysRoleVo::getRoleName);
    }

    /**
     * 查询用户所属岗位组
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    public String selectUserPostGroup(Long userId) {
        List<SysPostVo> list = postMapper.selectPostsByUserId(userId);
        if (CollUtil.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        return StreamUtils.join(list, SysPostVo::getPostName);
    }

    /**
     * 校验用户名称是否唯一
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean checkUserNameUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getUserName, user.getStaffId())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验手机号码是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkPhoneUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验email是否唯一
     *
     * @param user 用户信息
     */
    @Override
    public boolean checkEmailUnique(SysUserBo user) {
        boolean exist = baseMapper.exists(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getEmail, user.getEmail())
            .ne(ObjectUtil.isNotNull(user.getUserId()), SysUser::getUserId, user.getUserId()));
        return !exist;
    }

    /**
     * 校验用户是否允许操作
     *
     * @param userId 用户ID
     */
    @Override
    public void checkUserAllowed(Long userId) {
        if (ObjectUtil.isNotNull(userId) && LoginHelper.isSuperAdmin(userId)) {
            throw new ServiceException("不允许操作超级管理员用户");
        }
    }

    /**
     * 校验用户是否有数据权限
     *
     * @param userId 用户id
     */
    @Override
    public void checkUserDataScope(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            return;
        }
        if (LoginHelper.isSuperAdmin()) {
            return;
        }
        if (baseMapper.countUserById(userId) == 0) {
            throw new ServiceException("没有权限访问用户数据！");
        }
    }

    /**
     * 新增保存用户信息
     *
     * @param user       用户信息
     * @param createType 创建方式 0:系统创建 1:excel导入
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertUser(SysUserBo user, Integer createType) {
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        sysUser.setUserName(user.getStaffId());
        sysUser.setCreateType(createType);
        // 新增用户信息
        int rows = baseMapper.insert(sysUser);
        user.setUserId(sysUser.getUserId());
        // 新增用户岗位关联
        // insertUserPost(user, false);
        // 新增用户与角色管理
        insertUserRole(user, false);
        if (createType.equals(0)) {
            SpringUtils.context().publishEvent(SysUserEvent.builder().users(List.of(sysUser)).build());
        }
        return rows;
    }

    /**
     * 注册用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    public boolean registerUser(SysUserBo user, String tenantId) {
        SysUser sysUser = MapstructUtils.convert(user, SysUser.class);
        sysUser.setTenantId(tenantId);
        return baseMapper.insert(sysUser) > 0;
    }

    /**
     * 修改保存用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
//    @CacheEvict(cacheNames = CacheNames.SYS_NICKNAME, key = "#user.userId")
    @Transactional(rollbackFor = Exception.class)
    public int updateUser(SysUserBo user) {
        // 新增用户与角色管理
        insertUserRole(user, true);
        // 新增用户与岗位管理
        // insertUserPost(user, true);

        // 先从数据库查询当前用户记录
        SysUser existingUser = baseMapper.selectById(user.getUserId());
        BeanUtil.copyProperties(user, existingUser);

        // 执行更新操作
        int flag = baseMapper.updateById(existingUser);
        if (flag < 1) {
            throw new ServiceException("修改用户" + user.getStaffName() + "信息失败");
        }
        SpringUtils.context().publishEvent(SysUserEvent.builder().users(List.of(existingUser)).build());
        return flag;
    }

    /**
     * 用户授权角色
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertUserAuth(Long userId, Long[] roleIds) {
        insertUserRole(userId, roleIds, true);
    }

    /**
     * 修改用户状态
     *
     * @param userId 用户ID
     * @param status 帐号状态
     * @return 结果
     */
    @Override
    public int updateUserStatus(Long userId, String status) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getStatus, status)
                .eq(SysUser::getUserId, userId));
    }

    /**
     * 修改用户基本信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @CacheEvict(cacheNames = CacheNames.SYS_NICKNAME, key = "#user.userId")
    @Override
    public int updateUserProfile(SysUserBo user) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getEmail, user.getEmail())
                .eq(SysUser::getUserId, user.getUserId()));
    }

    /**
     * 修改用户头像
     *
     * @param userId 用户ID
     * @param avatar 头像地址
     * @return 结果
     */
    @Override
    public boolean updateUserAvatar(Long userId, Long avatar) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getAvatar, avatar)
                .eq(SysUser::getUserId, userId)) > 0;
    }

    /**
     * 重置用户密码
     *
     * @param userId   用户ID
     * @param password 密码
     * @return 结果
     */
    @Override
    public int resetUserPwd(Long userId, String password) {
        return baseMapper.update(null,
            new LambdaUpdateWrapper<SysUser>()
                .set(SysUser::getPassword, password)
                .eq(SysUser::getUserId, userId));
    }

    /**
     * 新增用户角色信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
    private void insertUserRole(SysUserBo user, boolean clear) {
        this.insertUserRole(user.getUserId(), user.getRoleIds(), clear);
    }

    /**
     * 新增用户岗位信息
     *
     * @param user  用户对象
     * @param clear 清除已存在的关联数据
     */
//    private void insertUserPost(SysUserBo user, boolean clear) {
//        Long[] posts = user.getPostIds();
//        if (ArrayUtil.isNotEmpty(posts)) {
//            if (clear) {
//                // 删除用户与岗位关联
//                userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, user.getUserId()));
//            }
//            // 新增用户与岗位管理
//            List<SysUserPost> list = StreamUtils.toList(List.of(posts), postId -> {
//                SysUserPost up = new SysUserPost();
//                up.setUserId(user.getUserId());
//                up.setPostId(postId);
//                return up;
//            });
//            userPostMapper.insertBatch(list);
//        }
//    }

    /**
     * 新增用户角色信息
     *
     * @param userId  用户ID
     * @param roleIds 角色组
     * @param clear   清除已存在的关联数据
     */
    private void insertUserRole(Long userId, Long[] roleIds, boolean clear) {
        if (ArrayUtil.isNotEmpty(roleIds)) {
            List<Long> roleList = new ArrayList<>(List.of(roleIds));
            if (!LoginHelper.isSuperAdmin(userId)) {
                roleList.remove(SystemConstants.SUPER_ADMIN_ID);
            }

            // 如果角色列表为空，直接返回
            if (CollUtil.isEmpty(roleList)) {
                return;
            }

            // 判断是否具有此角色的操作权限
            List<SysRoleVo> roles = roleMapper.selectRoleList(
                new QueryWrapper<SysRole>().in("r.role_id", roleList));
            if (CollUtil.isEmpty(roles)) {
                throw new ServiceException("没有权限访问角色的数据");
            }
            if (clear) {
                // 删除用户与角色关联
                userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
            }
            // 新增用户与角色管理
            List<SysUserRole> list = StreamUtils.toList(roleList, roleId -> {
                SysUserRole ur = new SysUserRole();
                ur.setUserId(userId);
                ur.setRoleId(roleId);
                return ur;
            });
            userRoleMapper.insertBatch(list);
        }
    }

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserById(Long userId) {
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().eq(SysUserRole::getUserId, userId));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().eq(SysUserPost::getUserId, userId));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteById(userId);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteUserByIds(Long[] userIds) {
        for (Long userId : userIds) {
            checkUserAllowed(userId);
            checkUserDataScope(userId);
        }
        List<Long> ids = List.of(userIds);
        // 删除用户与角色关联
        userRoleMapper.delete(new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getUserId, ids));
        // 删除用户与岗位表
        userPostMapper.delete(new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getUserId, ids));
        // 防止更新失败导致的数据删除
        int flag = baseMapper.deleteByIds(ids);
        if (flag < 1) {
            throw new ServiceException("删除用户失败!");
        }
        return flag;
    }

    /**
     * 通过部门id查询当前部门所有用户
     *
     * @param deptId 部门ID
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectUserListByDept(Long deptId) {
        LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUser::getDeptId, deptId);
        lqw.orderByAsc(SysUser::getUserId);
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Cacheable(cacheNames = CacheNames.SYS_USER_NAME, key = "#userId")
    @Override
    public String selectUserNameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getUserName).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getUserName);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userId 用户ID
     * @return 用户账户
     */
    @Override
    @Cacheable(cacheNames = CacheNames.SYS_NICKNAME, key = "#userId")
    public String selectNicknameById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getStaffName).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getStaffName);
    }

    /**
     * 通过用户ID查询用户账户
     *
     * @param userIds 用户ID 多个用逗号隔开
     * @return 用户账户
     */
    @Override
    public String selectNicknameByIds(String userIds) {
        List<String> list = new ArrayList<>();
        for (Long id : StringUtils.splitTo(userIds, Convert::toLong)) {
            String nickname = SpringUtils.getAopProxy(this).selectNicknameById(id);
            if (StringUtils.isNotBlank(nickname)) {
                list.add(nickname);
            }
        }
        return String.join(StringUtils.SEPARATOR, list);
    }

    /**
     * 通过用户ID查询用户手机号
     *
     * @param userId 用户id
     * @return 用户手机号
     */
    @Override
    public String selectPhonenumberById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getPhonenumber).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getPhonenumber);
    }

    /**
     * 通过用户ID查询用户邮箱
     *
     * @param userId 用户id
     * @return 用户邮箱
     */
    @Override
    public String selectEmailById(Long userId) {
        SysUser sysUser = baseMapper.selectOne(new LambdaQueryWrapper<SysUser>()
            .select(SysUser::getEmail).eq(SysUser::getUserId, userId));
        return ObjectUtils.notNullGetter(sysUser, SysUser::getEmail);
    }

    /**
     * 通过用户ID查询用户列表
     *
     * @param userIds 用户ids
     * @return 用户列表
     */
    @Override
    public List<UserDTO> selectListByIds(List<Long> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return List.of();
        }
        List<SysUserVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getStatus, USER_ENABLE)
            .in(SysUser::getUserId, userIds));
        return BeanUtil.copyToList(list, UserDTO.class);
    }

    /**
     * 通过角色ID查询用户ID
     *
     * @param roleIds 角色ids
     * @return 用户ids
     */
    @Override
    public List<Long> selectUserIdsByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return List.of();
        }
        List<SysUserRole> userRoles = userRoleMapper.selectList(
            new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getRoleId, roleIds));
        return StreamUtils.toList(userRoles, SysUserRole::getUserId);
    }

    /**
     * 通过角色ID查询用户
     *
     * @param roleIds 角色ids
     * @return 用户
     */
    @Override
    public List<UserDTO> selectUsersByRoleIds(List<Long> roleIds) {
        if (CollUtil.isEmpty(roleIds)) {
            return List.of();
        }

        // 通过角色ID获取用户角色信息
        List<SysUserRole> userRoles = userRoleMapper.selectList(
            new LambdaQueryWrapper<SysUserRole>().in(SysUserRole::getRoleId, roleIds));

        // 获取用户ID列表
        Set<Long> userIds = StreamUtils.toSet(userRoles, SysUserRole::getUserId);

        return selectListByIds(new ArrayList<>(userIds));
    }

    /**
     * 通过部门ID查询用户
     *
     * @param deptIds 部门ids
     * @return 用户
     */
    @Override
    public List<UserDTO> selectUsersByDeptIds(List<Long> deptIds) {
        if (CollUtil.isEmpty(deptIds)) {
            return List.of();
        }
        List<SysUserVo> list = baseMapper.selectVoList(new LambdaQueryWrapper<SysUser>()
            .eq(SysUser::getStatus, SystemConstants.NORMAL)
            .in(SysUser::getDeptId, deptIds));
        return BeanUtil.copyToList(list, UserDTO.class);
    }

    /**
     * 通过岗位ID查询用户
     *
     * @param postIds 岗位ids
     * @return 用户
     */
    @Override
    public List<UserDTO> selectUsersByPostIds(List<Long> postIds) {
        if (CollUtil.isEmpty(postIds)) {
            return List.of();
        }

        // 通过岗位ID获取用户岗位信息
        List<SysUserPost> userPosts = userPostMapper.selectList(
            new LambdaQueryWrapper<SysUserPost>().in(SysUserPost::getPostId, postIds));

        // 获取用户ID列表
        Set<Long> userIds = StreamUtils.toSet(userPosts, SysUserPost::getUserId);

        return selectListByIds(new ArrayList<>(userIds));
    }

    /**
     * 根据条件查询用户列表
     *
     * @param user 用户信息
     * @return 用户信息集合信息
     */
    @Override
    public List<SysUserVo> selectUserList(SysUserBo user) {
        // 使用简单的查询方式，不使用表别名
        LambdaQueryWrapper<SysUser> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysUser::getDelFlag, "0"); // 使用实体类的属性引用而不是字符串
        if (ObjectUtil.isNotNull(user)) {
            lqw.like(StringUtils.isNotBlank(user.getStaffId()), SysUser::getStaffId, user.getStaffId());
            lqw.like(StringUtils.isNotBlank(user.getStaffName()), SysUser::getStaffName, user.getStaffName());
            lqw.eq(StringUtils.isNotBlank(user.getStatus()), SysUser::getStatus, user.getStatus());
            lqw.eq(ObjectUtil.isNotNull(user.getDeptId()), SysUser::getDeptId, user.getDeptId());
        }
        return baseMapper.selectVoList(lqw);
    }

    /**
     * 修改用户关系信息
     *
     * @param user 用户信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateUserRelationships(SysUserBo user) {
        SysUser sysUser = new SysUser();
        sysUser.setUserId(user.getUserId());
        sysUser.setTeamLeader(user.getTeamLeader());
        sysUser.setTeamManager(user.getTeamManager());
        sysUser.setDirectLeader(user.getDirectLeader());
        sysUser.setUpdateBy(user.getUpdateBy());
        return baseMapper.updateById(sysUser);
    }

}
