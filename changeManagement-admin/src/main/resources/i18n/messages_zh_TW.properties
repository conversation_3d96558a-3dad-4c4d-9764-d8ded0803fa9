#錯誤消息
not.null=* 必須填寫
user.jcaptcha.error=驗證碼錯誤
user.jcaptcha.expire=驗證碼已失效
user.not.exists=對不起, 您的賬號：{0} 不存在.
user.password.not.match=賬號或密碼錯誤
user.password.retry.limit.count=密碼輸入錯誤{0}次
user.password.retry.limit.exceed=密碼輸入錯誤{0}次，帳戶鎖定{1}分鐘
user.password.delete=對不起，您的賬號：{0} 已被刪除
user.blocked=對不起，您的賬號：{0} 已禁用，請聯繫管理員
role.blocked=角色已封禁，請聯繫管理員
user.logout.success=退出成功
length.not.valid=長度必須在{min}到{max}個字符之間
user.username.not.blank=用戶名不能為空
user.username.not.valid=* 2到20個漢字、字母、數字或下劃線組成，且必須以非數字開頭
user.username.length.valid=賬戶長度必須在{min}到{max}個字符之間
user.password.not.blank=用戶密碼不能為空
user.password.length.valid=用戶密碼長度必須在{min}到{max}個字符之間
user.password.not.valid=* 5-50個字符
user.password.rule.invalid=密碼不符合規則，請輸入8-20位包括大小寫數字特殊符號的密碼
user.email.not.valid=郵箱格式錯誤
user.email.not.blank=郵箱不能為空
user.phonenumber.not.blank=用戶手機號不能為空
user.mobile.phone.number.not.valid=手機號格式錯誤
user.login.success=登錄成功
user.register.success=註冊成功
user.register.save.error=保存用戶 {0} 失敗，註冊賬號已存在
user.register.error=註冊失敗，請聯繫系統管理人員
user.notfound=請重新登錄
user.forcelogout=管理員強制退出，請重新登錄
user.unknown.error=未知錯誤，請重新登錄
auth.grant.type.error=認證權限類型錯誤
auth.grant.type.blocked=認證權限類型已禁用
auth.grant.type.not.blank=認證權限類型不能為空
auth.clientid.not.blank=認證客戶端id不能為空
##文件上傳消息
upload.exceed.maxSize=上傳的文件大小超出限制的文件大小！<br/>允許的文件最大大小是：{0}MB！
upload.filename.exceed.length=上傳的文件名最長{0}個字符
##權限
no.permission=您沒有數據的權限，請聯繫管理員添加權限 [{0}]
no.create.permission=您沒有創建數據的權限，請聯繫管理員添加權限 [{0}]
no.update.permission=您沒有修改數據的權限，請聯繫管理員添加權限 [{0}]
no.delete.permission=您沒有刪除數據的權限，請聯繫管理員添加權限 [{0}]
no.export.permission=您沒有導出數據的權限，請聯繫管理員添加權限 [{0}]
no.view.permission=您沒有查看數據的權限，請聯繫管理員添加權限 [{0}]
repeat.submit.message=不允許重複提交，請稍候再試
rate.limiter.message=訪問過於頻繁，請稍候再試
sms.code.not.blank=短信驗證碼不能為空

# 變更申請刪除相關錯誤消息
error.user.not.logged.in=用戶未登錄或用戶信息異常
error.change.info.not.found=變更申請不存在
error.change.item.not.found=變更項記錄不存在
error.change.non.draft.cannot.delete=當前非草稿狀態，不允許刪除
error.change.only.creator.can.delete=只能刪除自己創建的變更申請
error.change.item.delete.failed=刪除變更項記錄失敗
error.change.info.delete.failed=刪除變更申請記錄失敗
sms.code.retry.limit.count=短信驗證碼輸入錯誤{0}次
sms.code.retry.limit.exceed=短信驗證碼輸入錯誤{0}次，帳戶鎖定{1}分鐘
email.code.not.blank=郵箱驗證碼不能為空
email.code.retry.limit.count=郵箱驗證碼輸入錯誤{0}次
email.code.retry.limit.exceed=郵箱驗證碼輸入錯誤{0}次，帳戶鎖定{1}分鐘
xcx.code.not.blank=小程序[code]不能為空
social.source.not.blank=第三方登錄平台[source]不能為空
social.code.not.blank=第三方登錄平台[code]不能為空
social.state.not.blank=第三方登錄平台[state]不能為空
##租戶
tenant.number.not.blank=租戶編號不能為空
tenant.not.exists=對不起, 您的租戶不存在，請聯繫管理員
tenant.blocked=對不起，您的租戶已禁用，請聯繫管理員
tenant.expired=對不起，您的租戶已過期，請聯繫管理員

id.not.null=ID不能為空

##配置信息
conf.type.not.null=類型不能為空
conf.name.not.null=名稱不能為空
conf.name.size=名稱長度不能超過100個字符
conf.status.not.null=狀態不能為空
conf.description.size=描述長度不能超過200個字符

##應用管理
app.name.not.blank=應用名稱不能為空
app.name.size=應用名稱長度不能超過100個字符
app.business.owner.id.not.blank=BusinessOwnerId不能為空
app.business.owner.not.blank=BusinessOwner不能為空
app.team.leader.id.not.blank=TeamLeaderId不能為空
app.team.leader.not.blank=TeamLeader不能為空
app.team.id.not.null=Team_ID不能為空
app.team.name.not.blank=Team_Name不能為空
app.category.id.not.null=分類編號不能為空
app.category.not.blank=分類不能為空

##變更信息
entity.id=ID
entity.title=變更標題
entity.requesterId=申請人ID
entity.requesterName=申請人名稱
entity.teamId=變更組ID
entity.teamName=變更組名
entity.isUrgentChange=是否緊急變更
entity.changeType=變更類型
entity.isNetworkFreezeChange=是否封網變更
entity.categoryIds=分類id
entity.categoryNameList=分類名稱
entity.applicationId=應用/系統id
entity.applicationName=應用/系統名稱
entity.locationId=地點id
entity.locationName=地點名
entity.src=相關SRC/MSP
entity.priority=優先級
entity.planTimeStart=計劃開始時間
entity.planTimeEnd=計劃結束時間
entity.temporaryUrl=臨時訪問鏈接
entity.changeReason=變更原因
entity.changeDescription=變更描述
entity.fileUrl=附件地址
entity.affectedDescription=影響描述
entity.affectedTime=影響時間
entity.affectedUser=影響用戶
entity.affectedApplicationIds=影響系統ids
entity.affectedApplicationName=影響系統名稱
entity.affectedDeviceIds=影響設備ids
entity.affectedDeviceName=影響設備名
entity.notificationEmail=通知電郵地址
entity.notificationCcEmail=通知電郵抄送地址
entity.serviceDeliveryTeamIds=服務提交團隊ids
entity.serviceDeliveryTeamName=服務提交團隊
entity.teamLeaderIds=Team Leader ID
entity.teamLeaderName=Team Leader
entity.changeApproverIds=變更審批人id
entity.changeApproverName=變更審批人
entity.applicationOwnerIds=系統所有人id
entity.applicationOwner=系統所有人
entity.changeImplementerIds=變更實施人ids
entity.changeImplementerName=變更實施人
entity.changeVerifierIds=變更驗證人ids
entity.changeVerifierName=變更驗證人
entity.changeOwnerIds=變更所有者id
entity.changeOwnerName=變更所有者
entity.deptLeaderIds=部門負責人id
entity.deptLeaderName=部門負責人
entity.urgentChangeInspectorIds=緊急變更核查人ids
entity.urgentChangeInspector=緊急變更核查人
entity.systemsAffectedNo=受影響系統個數
entity.importantUsers=受影響重要用戶
entity.fallback=回退/回滾
entity.complexity=變更複雜性/實施經驗
entity.riskLevel=風險等級
entity.riskScore=風險得分
entity.requestDoc=需求文檔
entity.requestDocFileIds=需求文檔附件ids
entity.testDoc=測試文檔
entity.testDocFileIds=測試檔附件ids
entity.codeReview=代碼複查
entity.codeReviewFileIds=代碼複查附件ids
entity.rollOutPlan=上線計劃
entity.backoutPlan=回退計劃
entity.checkList=上線檢查清單
entity.reviewCheckList=核查清單
entity.emailContent=郵件內容

# Excel表頭國際化
excel.header.changeCode=變更編號
excel.header.title=變更標題
excel.header.stage=變更階段
excel.header.processorName=當前處理人
excel.header.teamName=變更組
excel.header.isUrgentChange=是否緊急變更
excel.header.isNetworkFreezeChange=是否封網變更
excel.header.frozen=是否封網變更
excel.header.priority=優先級
excel.header.createTime=創建時間
excel.header.requesterName=申請人
excel.header.planTimeStart=計劃開始時間
excel.header.planTimeEnd=計劃結束時間
excel.header.riskLevel=風險等級
excel.header.riskScore=風險得分
excel.header.changeType=變更類型
excel.header.locationName=地點名稱
excel.header.affectedUser=影響用戶
excel.header.systemsAffectedNo=受影響系統個數
excel.header.importantUsers=受影響重要用戶
excel.header.fallback=回退/回滾
excel.header.complexity=變更複雜性/實施經驗
excel.header.processTime=變更耗時
excel.sheet.changeList=變更列表

# 封網導出相關
excel.header.freezeCode=封網編號
excel.header.freezeTitle=封網標題
excel.header.freezeStage=封網階段
excel.header.freezeId=封網ID
excel.header.level=封網等級
excel.header.freezeApps=禁止操作
excel.header.periodAndArea=封網地區和時間
excel.header.publishTime=發布時間

# 變更階段枚舉國際化
enum.change.stage.DRAFT=草稿
enum.change.stage.SUBMITTED=計劃中
enum.change.stage.PENDING_APPROVAL=待審批
enum.change.stage.APPROVED=已審批
enum.change.stage.IMPLEMENTING=實施中
enum.change.stage.COMPLETED=已完成
enum.change.stage.REJECTED=已拒絕
enum.change.stage.ROLLED_BACK=已回滾
enum.change.stage.CANCELLED=已取消
enum.change.stage.PENDING_VERIFICATION=待驗證
enum.change.stage.DELAYED=已延期
enum.change.stage.OVER_7_DAYS=超過7天未關閉

# 是否選項枚舉國際化
enum.whether.YES=是
enum.whether.NO=否

# 優先級枚舉國際化
enum.level.HIGH=高
enum.level.MEDIUM=中
enum.level.LOW=低

# 受影響系統個數枚舉國際化
enum.systems.affected.NONE=無(1分)
enum.systems.affected.ONE_SYSTEM=一個(2分)
enum.systems.affected.TWO_SYSTEMS=兩個(3分)
enum.systems.affected.THREE_OR_MORE_SYSTEMS=三或更多個(4分)

# 受影響重要用戶枚舉國際化
enum.important.users.NONE=無(1分)
enum.important.users.INCONVENIENT_TO_CUSTOMER=對客戶不便(2分)
enum.important.users.OUTAGE_TO_ONE_CUSTOMER=影響一個客戶(3分)
enum.important.users.OUTAGE_TO_TWO_OR_MORE_CUSTOMERS=影響兩個或更多客戶(4分)

# 回退/回滾枚舉國際化
enum.fallback.NO_BACK_OUT=無回退(1分)
enum.fallback.BACK_OUT_TESTED=在維護前經過測試，並且還原措施可靠(2分)
enum.fallback.BACK_OUT_UNTESTED=沒有進行測試，或者還原步驟會受到實際環境中的不確定因素影響(5分)
enum.fallback.FALLBACK_NOT_POSSIBLE=回退不可行(10分)

# 變更複雜性/實施經驗枚舉國際化
enum.complexity.STANDARD_WITH_GOOD_EXP=具有良好經驗的標準技術(1分)
enum.complexity.STANDARD_WITH_LESS_EXP=經驗較少的標準技術(5分)
enum.complexity.NEW_WITH_VENDOR_SUPPORT=供應商支持的新技術(4分)
enum.complexity.NEW_WITHOUT_VENDOR_SUPPORT=無供應商支持的新技術(8分)

# 封網等級枚舉國際化
enum.network.freeze.level.LEVEL_ONE=一級
enum.network.freeze.level.LEVEL_TWO=二級
enum.network.freeze.level.LEVEL_THREE=三級

# 封網階段枚舉國際化
enum.network.freeze.stage.DRAFT=草稿
enum.network.freeze.stage.SUBMITTED=計劃中
enum.network.freeze.stage.PUBLISHED=已發佈
enum.network.freeze.stage.REJECTED=已拒絕

# 上传信息

# 封网地点和时间
network.freeze.location=地點
network.freeze.start.time=開始時間
network.freeze.end.time=結束時間

# 封網區域衝突
network.freeze.area.conflict=區域 {0} 在時間段 {1} 至 {2} 與已有封網申請（編號：{3}）存在衝突，請調整

# 封網審批權限
network.freeze.no.permission.approval=無權限審批{0}

# 變更審批權限
change.no.permission.approval=無此權限審批{0}

# 不同配置類型名稱重複提示
conf.name.duplicate.classification=分類名稱已存在
conf.name.duplicate.network.blocking.area=封網地區名稱已存在
conf.name.duplicate.change.location=變更地點名稱已存在
conf.name.duplicate.affected.device=受影響設備名稱已存在
conf.name.duplicate.maintenance.kind=維護種類名稱已存在

# 用戶實體校驗
user.id.not.null=用戶ID不能為空
user.staffid.not.null=賬號ID不能為空
user.staffid.length.valid=賬號ID長度不能超過{max}個字符
user.username.xss.invalid=賬號名稱不能包含腳本字符

# 用户操作结果提示
user.add.fail.username.exist=新增用戶{0}失敗，登錄賬號已存在
user.add.fail.email.exist=新增用戶{0}失敗，郵箱地址已存在
user.edit.fail.username.exist=修改用戶{0}失敗，登錄賬號已存在
user.edit.fail.email.exist=修改用戶{0}失敗，郵箱賬號已存在
user.staffid.rule.invalid=賬號ID不符合規則

# 用戶導入相關
import.result=處理結果： 本次上傳{0}個賬號，成功導入{1}個，失敗{2}個
import.fail.staffId=失敗StaffID: {0}, 原因: {1}
user.import.success=導入成功
user.import.update.success=更新成功
user.import.exists=已存在
user.import.relation.update.success=關係更新成功
user.import.relation.update.fail=關係更新失敗

# 角色相關消息
role.name.not.blank=角色名稱不能為空
role.name.length=角色名稱長度不能超過{max}個字符
role.key.not.blank=角色權限字符串不能為空
role.key.length=權限字符長度不能超過{max}個字符
role.sort.not.null=顯示順序不能為空
role.description.length=描述不能超過{max}個字符

# 部門相關消息
dept.name.not.blank=分組名稱不能為空
dept.name.length=分組名稱長度不能超過{max}個字符
dept.category.length=分組類別編碼長度不能超過{max}個字符
dept.order.not.null=顯示順序不能為空
dept.phone.length=聯繫電話長度不能超過{max}個字符
dept.email.format=郵箱格式不正確
dept.email.length=郵箱長度不能超過{max}個字符
dept.description.length=分組描述不能超過{max}個字符
dept.email.sign.length=郵件簽名不能超過{max}個字符

# 分組操作相關消息
dept.name.duplicate=新增分組{0}失敗，分組名稱已存在
dept.update.name.duplicate=修改分組{0}失敗，分組名稱已存在
dept.update.self=修改分組{0}失敗，上級分組不能是自己
dept.has.child=存在下級分組，不允許刪除
dept.has.user=分組存在用戶，不允許刪除
dept.has.post=分組存在崗位，不允許刪除
dept.init.not.allow.delete=初始化分組，不允許刪除
dept.no.permission=沒有權限訪問分組數據！

# 風險級別枚舉國際化
enum.risk.level.LOW=低
enum.risk.level.MEDIUM=中
enum.risk.level.HIGH=高
enum.risk.level.UNACCEPTABLE=不可接受
