#错误消息
not.null=* 必须填写
user.jcaptcha.error=验证码错误
user.jcaptcha.expire=验证码已失效
user.not.exists=对不起, 您的账号：{0} 不存在.
user.password.not.match=账号或密码错误
user.password.retry.limit.count=密码输入错误{0}次
user.password.retry.limit.exceed=密码输入错误{0}次，帐户锁定{1}分钟
user.password.delete=对不起，您的账号：{0} 已被删除
user.blocked=对不起，您的账号：{0} 已禁用，请联系管理员
role.blocked=角色已封禁，请联系管理员
user.logout.success=退出成功
length.not.valid=长度必须在{min}到{max}个字符之间
user.username.not.blank=用户名不能为空
user.username.not.valid=* 2到20个汉字、字母、数字或下划线组成，且必须以非数字开头
user.username.length.valid=账户长度必须在{min}到{max}个字符之间
user.password.not.blank=用户密码不能为空
user.password.length.valid=用户密码长度必须在{min}到{max}个字符之间
user.password.not.valid=* 5-50个字符
user.password.rule.invalid=密码不符合规则，请输入8-20位包括大小写数字特殊符号的密码
user.email.not.valid=邮箱格式错误
user.email.not.blank=邮箱不能为空
user.phonenumber.not.blank=用户手机号不能为空
user.mobile.phone.number.not.valid=手机号格式错误
user.login.success=登录成功
user.register.success=注册成功
user.register.save.error=保存用户 {0} 失败，注册账号已存在
user.register.error=注册失败，请联系系统管理人员
user.notfound=请重新登录
user.forcelogout=管理员强制退出，请重新登录
user.unknown.error=未知错误，请重新登录

auth.grant.type.error=认证权限类型错误
auth.grant.type.blocked=认证权限类型已禁用
auth.grant.type.not.blank=认证权限类型不能为空
auth.clientid.not.blank=认证客户端id不能为空
##文件上传消息
upload.exceed.maxSize=上传的文件大小超出限制的文件大小！<br/>允许的文件最大大小是：{0}MB！
upload.filename.exceed.length=上传的文件名最长{0}个字符
##权限
no.permission=您没有数据的权限，请联系管理员添加权限 [{0}]
no.create.permission=您没有创建数据的权限，请联系管理员添加权限 [{0}]
no.update.permission=您没有修改数据的权限，请联系管理员添加权限 [{0}]
no.delete.permission=您没有删除数据的权限，请联系管理员添加权限 [{0}]
no.export.permission=您没有导出数据的权限，请联系管理员添加权限 [{0}]
no.view.permission=您没有查看数据的权限，请联系管理员添加权限 [{0}]
repeat.submit.message=不允许重复提交，请稍候再试
rate.limiter.message=访问过于频繁，请稍候再试
sms.code.not.blank=短信验证码不能为空

# 变更申请删除相关错误消息
error.user.not.logged.in=用户未登录或用户信息异常
error.change.info.not.found=变更申请不存在
error.change.item.not.found=变更项记录不存在
error.change.non.draft.cannot.delete=当前非草稿状态，不允许删除
error.change.only.creator.can.delete=只能删除自己创建的变更申请
error.change.item.delete.failed=删除变更项记录失败
error.change.info.delete.failed=删除变更申请记录失败
sms.code.retry.limit.count=短信验证码输入错误{0}次
sms.code.retry.limit.exceed=短信验证码输入错误{0}次，帐户锁定{1}分钟
email.code.not.blank=邮箱验证码不能为空
email.code.retry.limit.count=邮箱验证码输入错误{0}次
email.code.retry.limit.exceed=邮箱验证码输入错误{0}次，帐户锁定{1}分钟
xcx.code.not.blank=小程序[code]不能为空
social.source.not.blank=第三方登录平台[source]不能为空
social.code.not.blank=第三方登录平台[code]不能为空
social.state.not.blank=第三方登录平台[state]不能为空
##租户
tenant.number.not.blank=租户编号不能为空
tenant.not.exists=对不起, 您的租户不存在，请联系管理员
tenant.blocked=对不起，您的租户已禁用，请联系管理员
tenant.expired=对不起，您的租户已过期，请联系管理员

id.not.null=ID不能为空

##配置信息
conf.type.not.null=类型不能为空
conf.name.not.null=名称不能为空
conf.name.size=名称长度不能超过100个字符
conf.status.not.null=状态不能为空
conf.description.size=描述长度不能超过200个字符

##应用管理
app.name.not.blank=应用名称不能为空
app.name.size=应用名称长度不能超过100个字符
app.business.owner.id.not.blank=BusinessOwnerId不能为空
app.business.owner.not.blank=BusinessOwner不能为空
app.team.leader.id.not.blank=TeamLeaderId不能为空
app.team.leader.not.blank=TeamLeader不能为空
app.team.id.not.null=Team_ID不能为空
app.team.name.not.blank=Team_Name不能为空
app.category.id.not.null=分类Id不能为空
app.category.not.blank=分类不能为空

##变更信息
entity.id=ID
entity.title=变更标题
entity.requesterId=申请人ID
entity.requesterName=申请人名称
entity.teamId=变更组ID
entity.teamName=变更组
entity.isUrgentChange=是否紧急变更
entity.changeType=变更类型
entity.isNetworkFreezeChange=是否封网变更
entity.categoryIds=分类id
entity.categoryNameList=分类名称
entity.applicationId=应用/系统id
entity.applicationName=应用/系统名称
entity.locationId=地点id
entity.locationName=地点
entity.src=相关SRC/MSP
entity.priority=优先级
entity.planTimeStart=计划开始时间
entity.planTimeEnd=计划结束时间
entity.temporaryUrl=临时访问链接
entity.changeReason=变更原因
entity.changeDescription=变更描述
entity.fileUrl=附件地址
entity.affectedDescription=影响描述
entity.affectedTime=影响时间
entity.affectedUser=影响用户
entity.affectedApplicationIds=影响系统ids
entity.affectedApplicationName=影响系统名称
entity.affectedDeviceIds=影响设备ids
entity.affectedDeviceName=影响设备名
entity.notificationEmail=通知电邮地址
entity.notificationCcEmail=通知电邮抄送地址
entity.serviceDeliveryTeamIds=服务提交团队ids
entity.serviceDeliveryTeamName=服务提交团队
entity.teamLeaderIds=Team Leader ID
entity.teamLeaderName=Team Leader
entity.changeApproverIds=变更审批人id
entity.changeApproverName=变更审批人
entity.applicationOwnerIds=系统所有人id
entity.applicationOwner=系统所有人
entity.changeImplementerIds=变更实施人ids
entity.changeImplementerName=变更实施人
entity.changeVerifierIds=变更验证人ids
entity.changeVerifierName=变更验证人
entity.changeOwnerIds=变更所有者id
entity.changeOwnerName=变更所有者
entity.deptLeaderIds=部门负责人id
entity.deptLeaderName=部门负责人
entity.urgentChangeInspectorIds=紧急变更核查人ids
entity.urgentChangeInspector=紧急变更核查人
entity.systemsAffectedNo=受影响系统个数
entity.importantUsers=受影响重要用户
entity.fallback=回退/回滚
entity.complexity=变更复杂性/实施经验
entity.riskLevel=风险等级
entity.riskScore=风险得分
entity.requestDoc=需求文档
entity.requestDocFileIds=需求文档附件ids
entity.testDoc=测试文档
entity.testDocFileIds=测试档附件ids
entity.codeReview=代码复查
entity.codeReviewFileIds=代码复查附件ids
entity.rollOutPlan=上线计划
entity.backoutPlan=回退计划
entity.checkList=上线检查清单
entity.reviewCheckList=核查清单
entity.emailContent=邮件内容

# Excel表头国际化
excel.header.changeCode=变更编号
excel.header.title=变更标题
excel.header.stage=变更阶段
excel.header.processorName=当前处理人
excel.header.teamName=变更组
excel.header.isUrgentChange=是否紧急变更
excel.header.isNetworkFreezeChange=是否封网变更
excel.header.frozen=是否封网变更
excel.header.priority=优先级
excel.header.createTime=创建时间
excel.header.requesterName=申请人
excel.header.planTimeStart=计划开始时间
excel.header.planTimeEnd=计划结束时间
excel.header.riskLevel=风险等级
excel.header.riskScore=风险得分
excel.header.changeType=变更类型
excel.header.locationName=地点
excel.header.affectedUser=影响用户
excel.header.systemsAffectedNo=受影响系统个数
excel.header.importantUsers=受影响重要用户
excel.header.fallback=回退/回滚
excel.header.complexity=变更复杂性/实施经验
excel.header.processTime=变更耗时
excel.sheet.changeList=变更列表

# 封网导出相关
excel.header.freezeCode=封网编号
excel.header.freezeTitle=封网标题
excel.header.freezeStage=封网阶段
excel.header.freezeId=封网ID
excel.header.level=封网等级
excel.header.freezeApps=禁止操作
excel.header.periodAndArea=封网地区和时间

# 变更阶段枚举国际化
enum.change.stage.DRAFT=草稿
enum.change.stage.SUBMITTED=计划中
enum.change.stage.PENDING_APPROVAL=待审批
enum.change.stage.APPROVED=已审批
enum.change.stage.IMPLEMENTING=实施中
enum.change.stage.COMPLETED=已完成
enum.change.stage.REJECTED=已拒绝
enum.change.stage.ROLLED_BACK=已回滚
enum.change.stage.CANCELLED=已取消
enum.change.stage.PENDING_VERIFICATION=待验证
enum.change.stage.DELAYED=已延期
enum.change.stage.OVER_7_DAYS=超过7天未关闭

# 是否选项枚举国际化
enum.whether.YES=是
enum.whether.NO=否

# 优先级枚举国际化
enum.level.HIGH=高
enum.level.MEDIUM=中
enum.level.LOW=低

# 受影响系统个数枚举国际化
enum.systems.affected.NONE=无(1分)
enum.systems.affected.ONE_SYSTEM=一个(2分)
enum.systems.affected.TWO_SYSTEMS=两个(3分)
enum.systems.affected.THREE_OR_MORE_SYSTEMS=三或更多个(4分)

# 受影响重要用户枚举国际化
enum.important.users.NONE=无(1分)
enum.important.users.INCONVENIENT_TO_CUSTOMER=对客户不便(2分)
enum.important.users.OUTAGE_TO_ONE_CUSTOMER=影响一个客户(3分)
enum.important.users.OUTAGE_TO_TWO_OR_MORE_CUSTOMERS=影响两个或更多客户(4分)

# 回退/回滚枚举国际化
enum.fallback.NO_BACK_OUT=无回退(1分)
enum.fallback.BACK_OUT_TESTED=在维护前经过测试，并且还原措施可靠(2分)
enum.fallback.BACK_OUT_UNTESTED=没有进行测试，或者还原步骤会受到实际环境中的不确定因素影响(5分)
enum.fallback.FALLBACK_NOT_POSSIBLE=回退不可行(10分)

# 变更复杂性/实施经验枚举国际化
enum.complexity.STANDARD_WITH_GOOD_EXP=具有良好经验的标准技术(1分)
enum.complexity.STANDARD_WITH_LESS_EXP=经验较少的标准技术(5分)
enum.complexity.NEW_WITH_VENDOR_SUPPORT=供应商支持的新技术(4分)
enum.complexity.NEW_WITHOUT_VENDOR_SUPPORT=无供应商支持的新技术(8分)

# 风险级别枚举国际化
enum.risk.level.LOW=低
enum.risk.level.MEDIUM=中
enum.risk.level.HIGH=高
enum.risk.level.UNACCEPTABLE=不可接受

# 上传信息
upload.success=上传成功
upload.failed=上传失败

# 封网等级枚举国际化
enum.network.freeze.level.LEVEL_ONE=一级
enum.network.freeze.level.LEVEL_TWO=二级
enum.network.freeze.level.LEVEL_THREE=三级

# 封网阶段枚举国际化
enum.network.freeze.stage.DRAFT=草稿
enum.network.freeze.stage.SUBMITTED=计划中
enum.network.freeze.stage.PUBLISHED=已发布
enum.network.freeze.stage.REJECTED=已拒绝

# 封网地点和时间
network.freeze.location=地点
network.freeze.start.time=开始时间
network.freeze.end.time=结束时间

# 封网区域冲突
network.freeze.area.conflict=区域 {0} 在时间段 {1} 至 {2} 与已有封网申请（编号：{3}）存在冲突，请调整

# 封网审批权限
network.freeze.no.permission.approval=无权限审批{0}
# 变更审批权限
change.no.permission.approval=无此权限审批{0}

# 不同配置类型名称重复提示
conf.name.duplicate.classification=分类名称已存在
conf.name.duplicate.network.blocking.area=封网地区名称已存在
conf.name.duplicate.change.location=变更地点名称已存在
conf.name.duplicate.affected.device=受影响设备名称已存在
conf.name.duplicate.maintenance.kind=维护种类名称已存在

# 用户实体校验
user.id.not.null=用户ID不能为空
user.staffid.not.null=账号ID不能为空
user.staffid.length.valid=账号ID长度不能超过{max}个字符
user.username.xss.invalid=账号名称不能包含脚本字符

# 用户操作结果提示
user.add.fail.username.exist=新增用户{0}失败，登录账号已存在
user.add.fail.email.exist=新增用户{0}失败，邮箱地址已存在
user.edit.fail.username.exist=修改用户{0}失败，登录账号已存在
user.edit.fail.email.exist=修改用户{0}失败，邮箱账号已存在
user.staffid.rule.invalid=账号ID不符合规则

# 用户导入相关
import.result=处理结果： 本次上传{0}个账号，成功导入{1}个，失败{2}个
import.fail.staffId=失败StaffID: {0}, 原因: {1}
user.import.success=导入成功
user.import.update.success=更新成功
user.import.exists=已存在
user.import.relation.update.success=关系更新成功
user.import.relation.update.fail=关系更新失败

# 角色相关消息
role.name.not.blank=角色名称不能为空
role.name.length=角色名称长度不能超过{max}个字符
role.key.not.blank=角色权限字符串不能为空
role.key.length=权限字符长度不能超过{max}个字符
role.sort.not.null=显示顺序不能为空
role.description.length=描述不能超过{max}个字符

# 部门相关消息
dept.name.not.blank=分组名称不能为空
dept.name.length=分组名称长度不能超过{max}个字符
dept.category.length=分组类别编码长度不能超过{max}个字符
dept.order.not.null=显示顺序不能为空
dept.phone.length=联系电话长度不能超过{max}个字符
dept.email.format=邮箱格式不正确
dept.email.length=邮箱长度不能超过{max}个字符
dept.description.length=分组描述不能超过{max}个字符
dept.email.sign.length=邮件签名不能超过{max}个字符

# 分组操作相关消息
dept.name.duplicate=新增分组{0}失败，分组名称已存在
dept.update.name.duplicate=修改分组{0}失败，分组名称已存在
dept.update.self=修改分组{0}失败，上级分组不能是自己
dept.has.child=存在下级分组，不允许删除
dept.has.user=分组存在用户，不允许删除
dept.has.post=分组存在岗位，不允许删除
dept.init.not.allow.delete=初始化分组，不允许删除
dept.no.permission=没有权限访问分组数据！
